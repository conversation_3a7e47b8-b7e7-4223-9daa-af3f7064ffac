import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      question, 
      choiceA, 
      choiceB, 
      choiceC, 
      choiceD, 
      correctAnswer, 
      userAnswer,
      userExplanation,
      topic,
      certificateName,
      priority,
      originalSuccessRate
    } = body;

    if (!question || !userExplanation || !correctAnswer || !userAnswer) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const isAnswerCorrect = userAnswer === correctAnswer;
    
    const prompt = `
You are an expert ${certificateName || 'certification'} instructor analyzing a student's explanation for why they believe their answer is correct.

QUESTION CONTEXT:
Question: ${question}
A) ${choiceA}
B) ${choiceB}
C) ${choiceC}
D) ${choiceD}

CORRECT ANSWER: ${correctAnswer}
STUDENT'S ANSWER: ${userAnswer}
STUDENT'S EXPLANATION: ${userExplanation}

ADDITIONAL CONTEXT:
- Topic: ${topic || 'Not specified'}
- Question Priority: ${priority || 'Not specified'}
- Original Success Rate: ${originalSuccessRate ? `${originalSuccessRate.toFixed(1)}%` : 'Not available'}

ANALYSIS TASK:
Analyze the student's explanation for their reasoning. Consider:

1. LOGICAL REASONING: Does their explanation demonstrate sound logical thinking?
2. CONCEPTUAL UNDERSTANDING: Do they understand the underlying concepts?
3. FACTUAL ACCURACY: Are the facts they mention correct?
4. COMPLETENESS: Does their explanation address the key aspects of the question?
5. MISCONCEPTIONS: Are there any misconceptions evident in their reasoning?

${isAnswerCorrect 
  ? `The student chose the CORRECT answer. Analyze whether their reasoning is sound and complete, or if they got lucky with incorrect reasoning.`
  : `The student chose an INCORRECT answer. Analyze their reasoning to identify where their thinking went wrong and what misconceptions led to the error.`
}

Provide your analysis in the following JSON format:
{
  "isExplanationCorrect": boolean, // Whether their reasoning is sound (can be false even if answer is correct)
  "score": number, // 1-10 scale for quality of reasoning
  "feedback": "Detailed feedback on their reasoning process",
  "suggestions": [
    "Specific suggestion 1 for improvement",
    "Specific suggestion 2 for improvement",
    "Specific suggestion 3 for improvement"
  ],
  "keyMisconceptions": [
    "Misconception 1 if any",
    "Misconception 2 if any"
  ],
  "strengthsInReasoning": [
    "Strength 1 in their thinking",
    "Strength 2 in their thinking"
  ],
  "studyRecommendations": [
    "Specific topic to review",
    "Specific concept to strengthen"
  ]
}

Be constructive and educational in your feedback. Focus on helping the student improve their reasoning process.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const analysisData = JSON.parse(jsonMatch[0]);
      
      // Validate the response structure
      if (typeof analysisData.isExplanationCorrect !== 'boolean' ||
          typeof analysisData.score !== 'number' ||
          typeof analysisData.feedback !== 'string' ||
          !Array.isArray(analysisData.suggestions)) {
        throw new Error('Invalid response structure');
      }

      return NextResponse.json({
        success: true,
        data: {
          ...analysisData,
          isAnswerCorrect,
          userAnswer,
          correctAnswer,
          analysisMetadata: {
            questionPriority: priority,
            originalSuccessRate,
            topic,
            analyzedAt: new Date().toISOString()
          }
        }
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response if JSON parsing fails
      return NextResponse.json({
        success: true,
        data: {
          isExplanationCorrect: isAnswerCorrect,
          score: isAnswerCorrect ? 7 : 3,
          feedback: text || "Unable to analyze explanation properly. Please try again.",
          suggestions: [
            "Review the key concepts related to this topic",
            "Practice explaining your reasoning step by step",
            "Focus on understanding the underlying principles"
          ],
          keyMisconceptions: [],
          strengthsInReasoning: [],
          studyRecommendations: [topic || "Review related topics"],
          isAnswerCorrect,
          userAnswer,
          correctAnswer,
          analysisMetadata: {
            questionPriority: priority,
            originalSuccessRate,
            topic,
            analyzedAt: new Date().toISOString()
          }
        }
      });
    }

  } catch (error) {
    console.error('Error in AnalyzeRepairCenterExplanation API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to analyze explanation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

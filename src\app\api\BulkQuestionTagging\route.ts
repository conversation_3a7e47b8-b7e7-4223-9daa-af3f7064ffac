import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow longer processing time for bulk operations
export const maxDuration = 300;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for individual question tagging
const QuestionTagSchema = z.object({
  questionId: z.string(),
  topic: z.string().describe('The main GDPR/CIPP-E topic this question tests'),
  confidence: z.number().min(0).max(1).describe('Confidence level of the tagging (0-1)'),
  subtopics: z.array(z.string()).optional().describe('Related GDPR subtopics if applicable')
});

// Schema for the bulk AI response
const BulkTaggingResponseSchema = z.object({
  questionTags: z.array(QuestionTagSchema).describe('Array of question tags with their GDPR topics')
});

// GDPR/CIPP-E specific topics for better AI guidance
const GDPR_TOPICS = [
  "Data Protection Principles",
  "Lawful Basis for Processing",
  "Consent Management",
  "Data Subject Rights",
  "Data Protection Impact Assessment (DPIA)",
  "Data Protection Officer (DPO)",
  "International Data Transfers",
  "Data Breach Notification",
  "Privacy by Design and Default",
  "Record Keeping and Documentation",
  "Supervisory Authority Powers",
  "Administrative Fines and Penalties",
  "Special Categories of Personal Data",
  "Children's Data Protection",
  "Marketing and Profiling",
  "Cookies and Online Tracking",
  "Data Retention and Erasure",
  "Data Processing Agreements",
  "Joint Controllers and Processors",
  "Certification and Codes of Conduct"
];

export async function POST(req: NextRequest) {
  try {
    // Extract data from request body
    const { questions, certificateName } = await req.json();

    // Validate required fields
    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return new NextResponse('Questions array is required and cannot be empty.', { status: 400 });
    }

    if (!certificateName || typeof certificateName !== 'string') {
      return new NextResponse('Certificate name is required.', { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return new NextResponse('Server configuration error: Missing API Key.', { status: 500 });
    }

    // Limit the number of questions to process at once to avoid timeout
    if (questions.length > 300) {
      return new NextResponse('Maximum 300 questions can be processed at once.', { status: 400 });
    }

    // Get the AI model - using gemini-2.5-pro as requested
    const model = google('gemini-2.5-flash');

    // Construct the expert prompt for GDPR/CIPP-E
    const expertPrompt = `
You are a world-class GDPR (General Data Protection Regulation) and CIPP/E (Certified Information Privacy Professional/Europe) expert. 

Your task is to analyze the following ${questions.length} multiple-choice questions and classify each one according to the specific GDPR/CIPP-E topic it tests.

Available GDPR/CIPP-E Topics:
${GDPR_TOPICS.map(topic => `- ${topic}`).join('\n')}

Questions to analyze:
${questions.map((q: { id: string; question: string; choiceA: string; choiceB: string; choiceC: string; choiceD: string; correctAnswer: string }, index: number) => `
Question ${index + 1} (ID: ${q.id}):
"${q.question}"

Choices:
A) ${q.choiceA}
B) ${q.choiceB}
C) ${q.choiceC}
D) ${q.choiceD}
Correct Answer: ${q.correctAnswer}
`).join('\n---\n')}

For each question, identify:
1. The most appropriate GDPR/CIPP-E topic from the list above
2. Your confidence level (0-1) in this classification
3. Any relevant subtopics (optional)

Be precise and use the exact topic names from the provided list. If a question doesn't fit perfectly into one topic, choose the most relevant one and adjust confidence accordingly.

Focus on what specific GDPR concept, principle, or requirement the question is testing. Consider the legal framework, practical applications, and compliance requirements.
`;

    // Call the AI model to generate structured response
    const result = await generateObject({
      model: model,
      prompt: expertPrompt,
      schema: BulkTaggingResponseSchema,
    });

    // Return the structured response
    return NextResponse.json({
      success: true,
      data: result.object.questionTags,
      processedCount: questions.length
    });

  } catch (error) {
    console.error('Error generating bulk question tags:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return new NextResponse('API configuration error.', { status: 500 });
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return new NextResponse('Service temporarily unavailable. Please try again later.', { status: 503 });
      }
      if (error.message.includes('timeout')) {
        return new NextResponse('Request timeout. Try processing fewer questions at once.', { status: 408 });
      }
    }
    
    return new NextResponse('Failed to generate question tags.', { status: 500 });
  }
}

import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  getDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  Timestamp 
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

export interface Certificate {
  id?: string;
  userId: string;
  name: string;
  description: string;
  provider: string;
  targetDate: string;
  priority: 'low' | 'medium' | 'high';
  status: 'planning' | 'studying' | 'scheduled' | 'completed' | 'expired';
  notes: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  completedAt?: Timestamp;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

export interface CreateCertificateData {
  name: string;
  description: string;
  provider: string;
  targetDate: string;
  priority: 'low' | 'medium' | 'high';
  notes: string;
}

export interface UpdateCertificateData {
  name?: string;
  description?: string;
  provider?: string;
  targetDate?: string;
  priority?: 'low' | 'medium' | 'high';
  status?: 'planning' | 'studying' | 'scheduled' | 'completed' | 'expired';
  notes?: string;
  expiryDate?: string;
  credentialId?: string;
  credentialUrl?: string;
}

const COLLECTION_NAME = 'certificates';

/**
 * Create a new certificate
 */
export const createCertificate = async (
  userId: string, 
  certificateData: CreateCertificateData
): Promise<string> => {
  try {
    const certificatesRef = collection(firestore, COLLECTION_NAME);
    
    const newCertificate: Omit<Certificate, 'id'> = {
      userId,
      ...certificateData,
      status: 'planning',
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(certificatesRef, newCertificate);
    return docRef.id;
  } catch (error) {
    console.error('Error creating certificate:', error);
    throw new Error('Failed to create certificate');
  }
};

/**
 * Get all certificates for a user
 */
export const getUserCertificates = async (userId: string): Promise<Certificate[]> => {
  try {
    const certificatesRef = collection(firestore, COLLECTION_NAME);
    const q = query(
      certificatesRef,
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Certificate));
  } catch (error) {
    console.error('Error fetching user certificates:', error);
    throw new Error('Failed to fetch certificates');
  }
};

/**
 * Get a specific certificate by ID
 */
export const getCertificateById = async (
  certificateId: string, 
  userId: string
): Promise<Certificate | null> => {
  try {
    const certificateRef = doc(firestore, COLLECTION_NAME, certificateId);
    const certificateSnap = await getDoc(certificateRef);
    
    if (certificateSnap.exists()) {
      const certificateData = certificateSnap.data() as Certificate;
      
      // Verify the certificate belongs to the user
      if (certificateData.userId !== userId) {
        throw new Error('Unauthorized access to certificate');
      }
      
      return {
        id: certificateSnap.id,
        ...certificateData
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error fetching certificate:', error);
    throw new Error('Failed to fetch certificate');
  }
};

/**
 * Update a certificate
 */
export const updateCertificate = async (
  certificateId: string,
  userId: string,
  updateData: UpdateCertificateData
): Promise<void> => {
  try {
    const certificateRef = doc(firestore, COLLECTION_NAME, certificateId);
    
    // First verify the certificate belongs to the user
    const certificateSnap = await getDoc(certificateRef);
    if (!certificateSnap.exists()) {
      throw new Error('Certificate not found');
    }
    
    const certificateData = certificateSnap.data() as Certificate;
    if (certificateData.userId !== userId) {
      throw new Error('Unauthorized access to certificate');
    }
    
    const updatedData = {
      ...updateData,
      updatedAt: serverTimestamp(),
      // Add completedAt timestamp if status is being changed to completed
      ...(updateData.status === 'completed' && certificateData.status !== 'completed' && {
        completedAt: serverTimestamp()
      })
    };
    
    await updateDoc(certificateRef, updatedData);
  } catch (error) {
    console.error('Error updating certificate:', error);
    throw new Error('Failed to update certificate');
  }
};

/**
 * Delete a certificate
 */
export const deleteCertificate = async (
  certificateId: string,
  userId: string
): Promise<void> => {
  try {
    const certificateRef = doc(firestore, COLLECTION_NAME, certificateId);
    
    // First verify the certificate belongs to the user
    const certificateSnap = await getDoc(certificateRef);
    if (!certificateSnap.exists()) {
      throw new Error('Certificate not found');
    }
    
    const certificateData = certificateSnap.data() as Certificate;
    if (certificateData.userId !== userId) {
      throw new Error('Unauthorized access to certificate');
    }
    
    await deleteDoc(certificateRef);
  } catch (error) {
    console.error('Error deleting certificate:', error);
    throw new Error('Failed to delete certificate');
  }
};

/**
 * Get certificates by status
 */
export const getCertificatesByStatus = async (
  userId: string,
  status: Certificate['status']
): Promise<Certificate[]> => {
  try {
    const certificatesRef = collection(firestore, COLLECTION_NAME);
    const q = query(
      certificatesRef,
      where('userId', '==', userId),
      where('status', '==', status),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Certificate));
  } catch (error) {
    console.error('Error fetching certificates by status:', error);
    throw new Error('Failed to fetch certificates by status');
  }
};

/**
 * Get certificates by priority
 */
export const getCertificatesByPriority = async (
  userId: string,
  priority: Certificate['priority']
): Promise<Certificate[]> => {
  try {
    const certificatesRef = collection(firestore, COLLECTION_NAME);
    const q = query(
      certificatesRef,
      where('userId', '==', userId),
      where('priority', '==', priority),
      orderBy('createdAt', 'desc')
    );
    
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Certificate));
  } catch (error) {
    console.error('Error fetching certificates by priority:', error);
    throw new Error('Failed to fetch certificates by priority');
  }
};

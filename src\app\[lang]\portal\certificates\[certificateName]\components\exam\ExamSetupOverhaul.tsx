"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import { generateExamAnalytics, ExamAnalytics } from "@/Firebase/firestore/services/ExamUtilsService";
import { getCurrentAnswerFullBankAttempt, AnswerFullBankAttempt } from "@/Firebase/firestore/services/AnswerFullBankService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import {
  BookOpen,
  Target,
  BarChart3,
  FileText,
  Play,
  TrendingUp,
  CheckCircle,
  Trophy,
  Calendar,
  Loader2,
  Al<PERSON><PERSON>ircle,
  Sparkles
} from "lucide-react";

interface ExamSetupOverhaulProps {
  certificate: Certificate;
  onStartExam: (examType: 'practice' | 'normal', questionCount: number) => void;
  onViewAnalytics: () => void;
  onStartAnswerFullBank: () => void;
}

export default function ExamSetupOverhaul({ certificate, onStartExam, onViewAnalytics, onStartAnswerFullBank }: ExamSetupOverhaulProps) {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [analytics, setAnalytics] = useState<ExamAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'practice' | 'normal'>('practice');
  const [questionCount, setQuestionCount] = useState([20]);
  const [answerFullBankProgress, setAnswerFullBankProgress] = useState<AnswerFullBankAttempt | null>(null);
  
  const { user } = useAuth();
  const { toast } = useToast();

  const loadData = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const [questionsData, analyticsData, answerFullBankData] = await Promise.all([
        getCertificateQuestions(user.uid, certificate.id),
        generateExamAnalytics(user.uid, certificate.id),
        getCurrentAnswerFullBankAttempt(user.uid, certificate.id)
      ]);

      setQuestions(questionsData);
      setAnalytics(analyticsData);
      setAnswerFullBankProgress(answerFullBankData);
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load exam data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    if (user?.uid && certificate.id) {
      loadData();
    }
  }, [user?.uid, certificate.id, loadData]);

  const handleStartExam = () => {
    if (questions.length === 0) {
      toast({
        title: "No Questions Available",
        description: "Please add questions to the question bank first.",
        variant: "destructive",
      });
      return;
    }

    if (questionCount[0] > questions.length) {
      toast({
        title: "Not Enough Questions",
        description: `You can only select up to ${questions.length} questions.`,
        variant: "destructive",
      });
      return;
    }

    onStartExam(selectedMode, questionCount[0]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading exam setup...</p>
        </div>
      </div>
    );
  }

  const maxQuestions = Math.min(questions.length, 100);
  const taggedQuestions = questions.filter(q => q.category);
  const topicCounts = questions.reduce((acc, q) => {
    const topic = q.category || 'Untagged';
    acc[topic] = (acc[topic] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
        {/* Header */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Take Exam
          </h1>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
            Choose your exam mode and start practicing
          </p>
        </div>

        <div className="flex flex-col lg:grid lg:grid-cols-4 gap-4 sm:gap-6 lg:gap-8">
          {/* Main Content - Middle Section */}
          <div className="lg:col-span-3 space-y-4 sm:space-y-6 lg:space-y-8 order-2 lg:order-1">
            {/* Question Bank KPIs */}
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl">
              <CardHeader className="pb-3 sm:pb-6">
                <CardTitle className="flex items-center space-x-2 sm:space-x-3">
                  <div className="p-1.5 sm:p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                    <BookOpen className="h-4 w-4 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h2 className="text-base sm:text-xl font-semibold">Question Bank Overview</h2>
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 font-normal">
                      Your practice question statistics
                    </p>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
                  <div className="text-center p-3 sm:p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-xl border border-blue-100 dark:border-blue-900/50">
                    <div className="p-2 sm:p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full w-fit mx-auto mb-2 sm:mb-3">
                      <FileText className="h-4 w-4 sm:h-6 sm:w-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {questions.length}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Total Questions</div>
                  </div>

                  <div className="text-center p-3 sm:p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-xl border border-green-100 dark:border-green-900/50">
                    <div className="p-2 sm:p-3 bg-green-100 dark:bg-green-900/30 rounded-full w-fit mx-auto mb-2 sm:mb-3">
                      <Sparkles className="h-4 w-4 sm:h-6 sm:w-6 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {taggedQuestions.length}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">AI Tagged</div>
                  </div>

                  <div className="text-center p-3 sm:p-4 bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30 rounded-xl border border-purple-100 dark:border-purple-900/50">
                    <div className="p-2 sm:p-3 bg-purple-100 dark:bg-purple-900/30 rounded-full w-fit mx-auto mb-2 sm:mb-3">
                      <BarChart3 className="h-4 w-4 sm:h-6 sm:w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {Object.keys(topicCounts).length}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Topics</div>
                  </div>

                  <div className="text-center p-3 sm:p-4 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 rounded-xl border border-orange-100 dark:border-orange-900/50">
                    <div className="p-2 sm:p-3 bg-orange-100 dark:bg-orange-900/30 rounded-full w-fit mx-auto mb-2 sm:mb-3">
                      <Trophy className="h-4 w-4 sm:h-6 sm:w-6 text-orange-600 dark:text-orange-400" />
                    </div>
                    <div className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-1">
                      {analytics?.totalAttempts || 0}
                    </div>
                    <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Attempts</div>
                  </div>
                </div>

                {questions.length === 0 && (
                  <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-950/30 rounded-xl border border-amber-200 dark:border-amber-800/50">
                    <div className="flex items-start space-x-3">
                      <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-amber-800 dark:text-amber-200 font-medium">No questions available</p>
                        <p className="text-amber-700 dark:text-amber-300 text-sm mt-1">
                          Please add questions to the Question Bank first to start taking exams.
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Exam Modes */}
            <div className="space-y-6">
              <div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  Choose Your Exam Mode
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Select the type of exam experience you want
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Practice Mode */}
                <Card
                  className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                    selectedMode === 'practice'
                      ? 'ring-2 ring-green-500 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 shadow-lg'
                      : 'hover:shadow-md bg-white dark:bg-gray-800'
                  }`}
                  onClick={() => setSelectedMode('practice')}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-xl ${
                          selectedMode === 'practice'
                            ? 'bg-green-100 dark:bg-green-900/30'
                            : 'bg-gray-100 dark:bg-gray-700'
                        }`}>
                          <BookOpen className={`h-6 w-6 ${
                            selectedMode === 'practice'
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-gray-600 dark:text-gray-400'
                          }`} />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Practice Mode
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Learn with immediate feedback
                          </p>
                        </div>
                      </div>
                      {selectedMode === 'practice' && (
                        <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        'Immediate answer feedback',
                        'Explanation after each question',
                        'No time pressure',
                        'Perfect for learning'
                      ].map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            selectedMode === 'practice' ? 'bg-green-500' : 'bg-gray-400'
                          }`} />
                          <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    {selectedMode === 'practice' && questions.length > 0 && (
                      <div className="mt-4 p-3 bg-green-100 dark:bg-green-900/20 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-green-800 dark:text-green-300">
                            Available Questions
                          </span>
                          <Badge className="bg-green-600 text-white">
                            {questions.length} questions
                          </Badge>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Normal Mode */}
                <Card
                  className={`cursor-pointer transition-all duration-300 hover:shadow-lg ${
                    selectedMode === 'normal'
                      ? 'ring-2 ring-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 shadow-lg'
                      : 'hover:shadow-md bg-white dark:bg-gray-800'
                  }`}
                  onClick={() => setSelectedMode('normal')}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`p-3 rounded-xl ${
                          selectedMode === 'normal'
                            ? 'bg-blue-100 dark:bg-blue-900/30'
                            : 'bg-gray-100 dark:bg-gray-700'
                        }`}>
                          <Target className={`h-6 w-6 ${
                            selectedMode === 'normal'
                              ? 'text-blue-600 dark:text-blue-400'
                              : 'text-gray-600 dark:text-gray-400'
                          }`} />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                            Normal Mode
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Real exam simulation
                          </p>
                        </div>
                      </div>
                      {selectedMode === 'normal' && (
                        <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        'Results shown at the end',
                        'Timed exam experience',
                        'Real exam simulation',
                        'Detailed final report'
                      ].map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className={`w-2 h-2 rounded-full ${
                            selectedMode === 'normal' ? 'bg-blue-500' : 'bg-gray-400'
                          }`} />
                          <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                        </div>
                      ))}
                    </div>
                    
                    {selectedMode === 'normal' && questions.length > 0 && (
                      <div className="mt-4 p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                            Available Questions
                          </span>
                          <Badge className="bg-blue-600 text-white">
                            {questions.length} questions
                          </Badge>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* AnswerFull Question Bank Mode */}
                <Card
                  className="cursor-pointer transition-all duration-300 hover:shadow-lg hover:shadow-md bg-white dark:bg-gray-800"
                  onClick={onStartAnswerFullBank}
                >
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="p-3 rounded-xl bg-purple-100 dark:bg-purple-900/30">
                          <BookOpen className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                        </div>
                        <div>
                          <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                            AnswerFull Question Bank
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            Answer all questions in order
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {[
                        'Answer all questions sequentially',
                        'Immediate explanations for wrong answers',
                        'Track progress through entire bank',
                        'Navigate between questions easily'
                      ].map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <div className="w-2 h-2 rounded-full bg-purple-500" />
                          <span className="text-sm text-gray-600 dark:text-gray-400">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {questions.length > 0 && (
                      <div className="mt-4 space-y-3">
                        <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-purple-800 dark:text-purple-300">
                              Available Questions
                            </span>
                            <Badge className="bg-purple-600 text-white">
                              {questions.length} questions
                            </Badge>
                          </div>
                        </div>

                        {answerFullBankProgress && (
                          <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-blue-800 dark:text-blue-300">
                                Progress Found
                              </span>
                              <Badge className="bg-blue-600 text-white">
                                {answerFullBankProgress.answeredQuestions}/{answerFullBankProgress.totalQuestions}
                              </Badge>
                            </div>
                            <div className="text-xs text-blue-700 dark:text-blue-300">
                              Continue from question {answerFullBankProgress.currentQuestionIndex + 1}
                            </div>
                            <div className="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2 mt-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                style={{
                                  width: `${(answerFullBankProgress.answeredQuestions / answerFullBankProgress.totalQuestions) * 100}%`
                                }}
                              ></div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Question Count Selection */}
            {questions.length > 0 && (
              <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl">
                <CardHeader>
                  <CardTitle className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-xl">
                      <FileText className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">Question Count</h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 font-normal">
                        Choose how many questions for your exam
                      </p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Live Preview */}
                    <div className="bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-3">
                          <div className={`text-3xl font-bold ${
                            selectedMode === 'practice' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'
                          }`}>
                            {questionCount[0]}
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">Questions Selected</div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              {selectedMode === 'practice' ? 'Practice Mode' : 'Normal Mode'}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-medium text-gray-900 dark:text-white">
                            ~{Math.ceil(questionCount[0] * 1.5)} min
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400">Estimated time</div>
                        </div>
                      </div>

                      {/* Topic Distribution Preview */}
                      {Object.keys(topicCounts).length > 0 && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Estimated topic distribution:
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {Object.entries(topicCounts).slice(0, 4).map(([topic, count]) => {
                              const estimatedQuestions = Math.max(1, Math.floor((questionCount[0] / Object.keys(topicCounts).length)));
                              return (
                                <div key={topic} className="flex items-center space-x-1 text-xs">
                                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                                  <span className="text-gray-600 dark:text-gray-400">
                                    {topic.length > 12 ? topic.substring(0, 12) + '...' : topic}
                                  </span>
                                  <span className="text-gray-500 dark:text-gray-500">
                                    (~{Math.min(estimatedQuestions, count)})
                                  </span>
                                </div>
                              );
                            })}
                            {Object.keys(topicCounts).length > 4 && (
                              <span className="text-xs text-gray-500 dark:text-gray-500">
                                +{Object.keys(topicCounts).length - 4} more
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Slider */}
                    <div className="space-y-4">
                      <Slider
                        value={questionCount}
                        onValueChange={setQuestionCount}
                        max={maxQuestions}
                        min={1}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                        <span>1 question</span>
                        <div className="flex space-x-3">
                          <button
                            onClick={() => setQuestionCount([10])}
                            className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                            disabled={maxQuestions < 10}
                          >
                            10
                          </button>
                          <button
                            onClick={() => setQuestionCount([20])}
                            className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                            disabled={maxQuestions < 20}
                          >
                            20
                          </button>
                          <button
                            onClick={() => setQuestionCount([50])}
                            className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                            disabled={maxQuestions < 50}
                          >
                            50
                          </button>
                        </div>
                        <span>{maxQuestions} questions</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6">
              <Button
                onClick={handleStartExam}
                disabled={questions.length === 0}
                size="lg"
                className={`group relative overflow-hidden px-8 py-4 text-lg ${
                  selectedMode === 'practice'
                    ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg shadow-green-500/25'
                    : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 shadow-lg shadow-blue-500/25'
                } disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
              >
                <div className="flex items-center space-x-3">
                  <Play className="h-6 w-6 transition-transform group-hover:scale-110" />
                  <span className="font-medium">
                    Start {selectedMode === 'practice' ? 'Practice' : 'Normal'} Exam
                  </span>
                  {questions.length > 0 && (
                    <Badge className="bg-white/20 text-white border-white/30">
                      {questionCount[0]} questions
                    </Badge>
                  )}
                </div>
                <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12" />
              </Button>

              <Button
                onClick={onViewAnalytics}
                size="lg"
                className="group relative overflow-hidden px-8 py-4 text-lg bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 text-gray-800 dark:text-gray-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center space-x-3">
                  <TrendingUp className="h-6 w-6 transition-transform group-hover:scale-110" />
                  <span className="font-medium">View Analytics</span>
                </div>
                <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12" />
              </Button>
            </div>
          </div>

          {/* Right Sidebar - Attempts History */}
          <div className="lg:col-span-1">
            <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Calendar className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                  <span>Recent Attempts</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {analytics && analytics.recentAttempts.length > 0 ? (
                  <div className="space-y-3">
                    {analytics.recentAttempts.slice(0, 5).map((attempt) => {
                      const scoreColor = (attempt.percentage || 0) >= 80
                        ? 'text-green-600 dark:text-green-400'
                        : (attempt.percentage || 0) >= 60
                        ? 'text-yellow-600 dark:text-yellow-400'
                        : 'text-red-600 dark:text-red-400';

                      return (
                        <div
                          key={attempt.id}
                          className="p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600"
                        >
                          <div className="flex items-center justify-between mb-2">
                            <Badge
                              variant={attempt.examType === 'practice' ? 'default' : 'secondary'}
                              className="text-xs"
                            >
                              {attempt.examType === 'practice' ? 'Practice' : 'Normal'}
                            </Badge>
                            <span className={`text-sm font-semibold ${scoreColor}`}>
                              {Math.round(attempt.percentage || 0)}%
                            </span>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
                            <div className="flex justify-between">
                              <span>Score:</span>
                              <span>{attempt.score}/{attempt.totalQuestions}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Time:</span>
                              <span>
                                {attempt.timeSpent ? 
                                  `${Math.floor(attempt.timeSpent / 60)}:${(attempt.timeSpent % 60).toString().padStart(2, '0')}` 
                                  : 'N/A'
                                }
                              </span>
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-500">
                              {attempt.completedAt?.toDate?.()?.toLocaleDateString('en-US', {
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              }) || 'Recently'}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                    
                    {analytics.totalAttempts > 5 && (
                      <Button
                        onClick={onViewAnalytics}
                        variant="ghost"
                        size="sm"
                        className="w-full text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
                      >
                        View All {analytics.totalAttempts} Attempts
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-6">
                    <div className="p-3 bg-gray-100 dark:bg-gray-700 rounded-full w-fit mx-auto mb-3">
                      <Trophy className="h-6 w-6 text-gray-400" />
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      No attempts yet
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-500">
                      Start your first exam to see history here
                    </p>
                  </div>
                )}

                {analytics && analytics.totalAttempts > 0 && (
                  <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                    <div className="grid grid-cols-2 gap-3 text-center">
                      <div>
                        <div className="text-lg font-semibold text-gray-900 dark:text-white">
                          {Math.round(analytics.averageScore)}%
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">Avg Score</div>
                      </div>
                      <div>
                        <div className="text-lg font-semibold text-gray-900 dark:text-white">
                          {Math.round(analytics.bestScore)}%
                        </div>
                        <div className="text-xs text-gray-600 dark:text-gray-400">Best Score</div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}

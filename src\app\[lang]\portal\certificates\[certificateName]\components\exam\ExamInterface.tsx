"use client";

import React, { useState, useEffect, useRef } from "react";
import { ExamAttempt, ExamQuestion } from "@/Firebase/firestore/services/ExamService";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/components/ui/use-toast";
import {
  Clock,
  ChevronLeft,
  ChevronRight,
  Flag,
  CheckCircle,
  XCircle,
  MessageCircle,
  Send,
  BookOpen,
  Target,
  HelpCircle,
  X
} from "lucide-react";

interface ExamInterfaceProps {
  attempt: ExamAttempt;
  questions: ExamQuestion[];
  onSubmitAnswer: (attemptId: string, questionId: string, answer: 'A' | 'B' | 'C' | 'D', timeSpent: number) => Promise<boolean>;
  onCompleteExam: () => void;
  onExitExam: () => void;
}

interface ChoiceExplanation {
  choice: 'A' | 'B' | 'C' | 'D';
  explanation: string;
  isCorrect: boolean;
  reasoning: string;
}

interface QuestionExplanation {
  explanation: string;
  choiceExplanations: ChoiceExplanation[];
  keyPoints: string[];
  relatedConcepts: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  studyTips: string[];
}

interface ChatMessage {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

interface QuestionStatus {
  answered: boolean;
  correct?: boolean;
  flagged: boolean;
  skipped: boolean;
}

export default function ExamInterface({
  attempt,
  questions,
  onSubmitAnswer,
  onCompleteExam,
  onExitExam
}: ExamInterfaceProps) {
  const { toast } = useToast();
  
  // Core exam state
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [showFeedback, setShowFeedback] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [examStartTime] = useState(Date.now());
  const [flaggedQuestions, setFlaggedQuestions] = useState<Set<number>>(new Set());

  // Local state to track current question's answer and correctness
  const [currentQuestionAnswer, setCurrentQuestionAnswer] = useState<'A' | 'B' | 'C' | 'D' | null>(null);
  const [currentQuestionCorrect, setCurrentQuestionCorrect] = useState<boolean | null>(null);

  // Question status tracking for all questions
  const [questionStatuses, setQuestionStatuses] = useState<Map<number, QuestionStatus>>(new Map());

  // Explanation and chat state
  const [showExplanation, setShowExplanation] = useState(false);
  const [currentExplanation, setCurrentExplanation] = useState<QuestionExplanation | null>(null);
  const [isLoadingExplanation, setIsLoadingExplanation] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isSendingMessage, setIsSendingMessage] = useState(false);


  // Ref to track if we're currently showing feedback to prevent unwanted resets
  const isShowingFeedbackRef = useRef(false);

  const currentQuestion = questions[currentQuestionIndex];
  const isPracticeMode = attempt.examType === 'practice';
  const answeredQuestions = questions.filter(q => q.userAnswer).length;
  const progress = (answeredQuestions / questions.length) * 100;

  // Helper functions for question status management
  const getQuestionStatus = (index: number): QuestionStatus => {
    const existing = questionStatuses.get(index);
    if (existing) return existing;
    
    const question = questions[index];
    return {
      answered: !!question.userAnswer || (index === currentQuestionIndex && !!currentQuestionAnswer),
      correct: question.isCorrect ?? (index === currentQuestionIndex ? currentQuestionCorrect ?? undefined : undefined),
      flagged: flaggedQuestions.has(index),
      skipped: false // We'll implement skip functionality
    };
  };

  const updateQuestionStatus = (index: number, updates: Partial<QuestionStatus>) => {
    setQuestionStatuses(prev => {
      const newMap = new Map(prev);
      const current = getQuestionStatus(index);
      newMap.set(index, { ...current, ...updates });
      return newMap;
    });
  };

  const getQuestionStatusColor = (index: number): string => {
    const status = getQuestionStatus(index);
    if (status.flagged) return 'bg-yellow-500';
    if (status.answered && status.correct === true) return 'bg-green-500';
    if (status.answered && status.correct === false) return 'bg-red-500';
    if (status.answered) return 'bg-blue-500';
    if (status.skipped) return 'bg-gray-400';
    return 'bg-gray-200 dark:bg-gray-700';
  };

  useEffect(() => {
    // Reset state when moving to a new question (only when question index changes)
    // Don't reset if we're currently showing feedback for this question
    if (!isShowingFeedbackRef.current) {
      setSelectedAnswer(currentQuestion?.userAnswer || null);
      setQuestionStartTime(Date.now());
      setShowFeedback(false);
      setShowExplanation(false);
      setShowChat(false);
      setChatMessages([]);

      // Reset local question state
      setCurrentQuestionAnswer(currentQuestion?.userAnswer || null);
      setCurrentQuestionCorrect(currentQuestion?.isCorrect || null);
      
      console.log('Question changed - resetting state:', {
        questionIndex: currentQuestionIndex,
        questionId: currentQuestion?.id,
        isShowingFeedback: isShowingFeedbackRef.current
      });
    } else {
      console.log('Skipping state reset - feedback is showing:', {
        questionIndex: currentQuestionIndex,
        isShowingFeedback: isShowingFeedbackRef.current
      });
    }
  }, [currentQuestionIndex, currentQuestion?.id, currentQuestion?.isCorrect, currentQuestion?.userAnswer]); // Include all used properties

  const handleAnswerSelect = (answer: 'A' | 'B' | 'C' | 'D') => {
    // Don't allow changing answers after submission
    if (currentQuestionAnswer || currentQuestion.userAnswer) {
      return;
    }
    // Don't allow selection when feedback is shown in practice mode
    if (isPracticeMode && showFeedback) {
      return;
    }
    setSelectedAnswer(answer);
  };

  const handleSubmitAnswer = async () => {
    if (!selectedAnswer || !currentQuestion.id) return;

    setIsSubmitting(true);
    try {
      const timeSpent = Math.floor((Date.now() - questionStartTime) / 1000);
      const isCorrect = await onSubmitAnswer(attempt.id!, currentQuestion.id!, selectedAnswer, timeSpent);

      // Update local state to track the current question's answer
      console.log('About to update local state:', {
        selectedAnswer,
        isCorrect,
        questionId: currentQuestion.id
      });
      
      setCurrentQuestionAnswer(selectedAnswer);
      setCurrentQuestionCorrect(isCorrect);

      // Update question status
      updateQuestionStatus(currentQuestionIndex, {
        answered: true,
        correct: isCorrect
      });

      console.log('Local question state updated:', {
        questionIndex: currentQuestionIndex,
        selectedAnswer,
        isCorrect,
        questionId: currentQuestion.id
      });

      if (isPracticeMode) {
        // Set ref to prevent state resets
        isShowingFeedbackRef.current = true;
        
        // Show feedback immediately
        setShowFeedback(true);

        // Debug logging
        console.log('Practice mode feedback activated:', {
          isCorrect,
          selectedAnswer,
          correctAnswer: currentQuestion.correctAnswer,
          questionId: currentQuestion.id,
          showFeedback: true,
          currentQuestionIndex,
          isShowingFeedbackRef: isShowingFeedbackRef.current
        });
      } else {
        // In normal mode, move to next question automatically
        if (currentQuestionIndex < questions.length - 1) {
          setCurrentQuestionIndex(currentQuestionIndex + 1);
        }
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < questions.length - 1) {
      // Reset the feedback ref when moving to next question
      isShowingFeedbackRef.current = false;
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      // Reset the feedback ref when moving to previous question
      isShowingFeedbackRef.current = false;
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleQuestionNavigation = (index: number) => {
    if (index >= 0 && index < questions.length && index !== currentQuestionIndex) {
      isShowingFeedbackRef.current = false;
      setCurrentQuestionIndex(index);
      setShowExplanation(false);
      setShowChat(false);
      setChatMessages([]);
    }
  };

  const handleExplainQuestion = async () => {
    if (!currentQuestion) return;
    
    setIsLoadingExplanation(true);
    try {
      const response = await fetch('/api/QuestionExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: currentQuestion.questionText,
          choiceA: currentQuestion.choiceA,
          choiceB: currentQuestion.choiceB,
          choiceC: currentQuestion.choiceC,
          choiceD: currentQuestion.choiceD,
          topic: currentQuestion.topic || 'GDPR/Privacy Law',
          certificateName: attempt.certificateId || 'Privacy Certification'
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get explanation');
      }

      const data = await response.json();
      setCurrentExplanation(data.data);
      setShowExplanation(true);
    } catch (error) {
      console.error('Error getting explanation:', error);
      toast({
        title: "Error",
        description: "Failed to load explanation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoadingExplanation(false);
    }
  };

  const handleSendChatMessage = async () => {
    if (!chatInput.trim() || !currentQuestion) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: chatInput.trim(),
      timestamp: Date.now()
    };

    setChatMessages(prev => [...prev, userMessage]);
    setChatInput('');
    setIsSendingMessage(true);

    try {
      console.log('Sending chat message:', userMessage.content);

      const response = await fetch('/api/QuestionChat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userMessage: userMessage.content,
          questionContext: {
            question: currentQuestion.questionText,
            choiceA: currentQuestion.choiceA,
            choiceB: currentQuestion.choiceB,
            choiceC: currentQuestion.choiceC,
            choiceD: currentQuestion.choiceD,
            topic: currentQuestion.topic || 'GDPR/Privacy Law'
          },
          chatHistory: chatMessages,
          certificateName: attempt.certificateId || 'Privacy Certification'
        }),
      });

      console.log('Response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', errorText);
        throw new Error(`Failed to get chat response: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      console.log('Chat response received:', data);

      if (!data.success || !data.message) {
        throw new Error('Invalid response format');
      }

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: formatAIResponse(data.message),
        timestamp: Date.now()
      };

      setChatMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      console.error('Error sending chat message:', error);

      // Add error message to chat
      const errorMessage: ChatMessage = {
        id: (Date.now() + 2).toString(),
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try asking your question again.',
        timestamp: Date.now()
      };
      setChatMessages(prev => [...prev, errorMessage]);

      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSendingMessage(false);
    }
  };

  const toggleFlag = () => {
    const newFlagged = new Set(flaggedQuestions);
    if (newFlagged.has(currentQuestionIndex)) {
      newFlagged.delete(currentQuestionIndex);
      updateQuestionStatus(currentQuestionIndex, { flagged: false });
    } else {
      newFlagged.add(currentQuestionIndex);
      updateQuestionStatus(currentQuestionIndex, { flagged: true });
    }
    setFlaggedQuestions(newFlagged);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getElapsedTime = () => {
    return Math.floor((Date.now() - examStartTime) / 1000);
  };

  const getChoiceStyle = (choice: 'A' | 'B' | 'C' | 'D') => {
    const isSelected = selectedAnswer === choice;
    const isAnswered = (currentQuestionAnswer || currentQuestion.userAnswer) === choice;

    // Debug logging for the first choice only to avoid spam
    if (choice === 'A') {
      console.log('getChoiceStyle debug:', {
        choice,
        showFeedback,
        isPracticeMode,
        correctAnswer: currentQuestion.correctAnswer,
        selectedAnswer,
        userAnswer: currentQuestion.userAnswer,
        currentQuestionAnswer,
        currentQuestionCorrect,
        isSelected,
        isAnswered,
        timestamp: Date.now()
      });
    }

    // Practice mode with feedback - show correct/incorrect colors
    if (showFeedback && isPracticeMode) {
      if (choice === currentQuestion.correctAnswer) {
        // Correct answer - always show in green
        return 'bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 border-2 border-green-500 text-green-800 dark:text-green-200 shadow-lg shadow-green-500/20';
      }
      if (isSelected && choice !== currentQuestion.correctAnswer) {
        // User's incorrect selection - show in red
        return 'bg-gradient-to-r from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30 border-2 border-red-500 text-red-800 dark:text-red-200 shadow-lg shadow-red-500/20';
      }
      // Other options when feedback is shown - muted
      return 'bg-gray-50 dark:bg-gray-800/50 border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-400 opacity-60';
    }

    // Before feedback or in normal mode
    if (isSelected || isAnswered) {
      return isPracticeMode
        ? 'bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-2 border-blue-500 text-blue-800 dark:text-blue-200 shadow-lg shadow-blue-500/20'
        : 'bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 border-2 border-blue-500 text-blue-800 dark:text-blue-200';
    }

    // Default unselected state
    return 'bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 hover:border-gray-400 dark:hover:border-gray-500 transition-all duration-200';
  };

  const getChoiceExplanation = (choice: 'A' | 'B' | 'C' | 'D'): string => {
    if (!currentExplanation || !currentExplanation.choiceExplanations) return '';

    // Find the specific explanation for this choice
    const choiceExplanation = currentExplanation.choiceExplanations.find(exp => exp.choice === choice);

    if (choiceExplanation) {
      return `${choiceExplanation.explanation} ${choiceExplanation.reasoning}`;
    }

    // Fallback: provide general explanation based on correctness
    if (choice === currentQuestion.correctAnswer) {
      return 'This is the correct answer. ' + (currentExplanation.keyPoints[0] || 'This option aligns with the relevant legal principles and requirements.');
    } else if (selectedAnswer === choice) {
      return 'This was your selection, but it\'s not the best answer. ' + (currentExplanation.keyPoints[1] || 'Consider reviewing the key concepts for this topic.');
    } else {
      return 'This option is not correct. ' + (currentExplanation.keyPoints[2] || 'While this might seem plausible, it doesn\'t fully address the question requirements.');
    }
  };

  const formatAIResponse = (text: string): string => {
    // Add line breaks after sentences that end with periods followed by capital letters
    let formatted = text.replace(/\. ([A-Z])/g, '.\n\n$1');

    // Add line breaks before common transition words/phrases
    formatted = formatted.replace(/(However,|Additionally,|Furthermore,|Moreover,|In contrast,|For example,|Specifically,|In summary,)/g, '\n\n$1');

    // Add line breaks before numbered or bulleted lists
    formatted = formatted.replace(/(\d+\.|•|\*)/g, '\n$1');

    // Clean up multiple line breaks
    formatted = formatted.replace(/\n{3,}/g, '\n\n');

    return formatted.trim();
  };

  const canSubmit = selectedAnswer && !currentQuestionAnswer && !currentQuestion.userAnswer;
  const canProceed = currentQuestionAnswer || currentQuestion.userAnswer || showFeedback;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      {/* Header */}
      <div className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200/50 dark:border-gray-700/50 sticky top-0 z-10 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
            <div className="flex items-center justify-between sm:justify-start sm:space-x-4">
              <div className="flex items-center space-x-2 sm:space-x-4">
                <div className={`px-2 sm:px-3 py-1 sm:py-1.5 rounded-full text-xs sm:text-sm font-medium ${
                  isPracticeMode
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                    : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                }`}>
                  {isPracticeMode ? 'Practice' : 'Normal'} Mode
                </div>
                <Badge variant="outline" className="text-xs sm:text-sm">
                  <span className="hidden sm:inline">Question </span>{currentQuestionIndex + 1}/{questions.length}
                </Badge>
              </div>
              <div className="flex items-center space-x-2 sm:hidden">
                <div className="flex items-center space-x-1 text-xs text-gray-600 dark:text-gray-400">
                  <Clock className="h-3 w-3" />
                  <span>{formatTime(getElapsedTime())}</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleFlag}
                  className={`p-1 ${
                    flaggedQuestions.has(currentQuestionIndex)
                      ? 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300'
                      : ''
                  }`}
                >
                  <Flag className="h-3 w-3" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-between sm:justify-end sm:space-x-4">
              <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                <Clock className="h-4 w-4" />
                <span>{formatTime(getElapsedTime())}</span>
              </div>
              <div className="hidden sm:block">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleFlag}
                  className={`${
                    flaggedQuestions.has(currentQuestionIndex)
                      ? 'bg-yellow-100 text-yellow-700 border-yellow-300 dark:bg-yellow-900/30 dark:text-yellow-300'
                      : ''
                  }`}
                >
                  <Flag className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center gap-2 sm:gap-3">
                {/* CIPP/E Exam Badge */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-2 sm:px-4 py-1 sm:py-2 rounded-full text-xs sm:text-sm font-semibold shadow-lg flex items-center gap-1 sm:gap-2">
                  <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-white rounded-full animate-pulse"></div>
                  <span className="hidden sm:inline">CIPP/E EXAM</span>
                  <span className="sm:hidden">CIPP/E</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onExitExam}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50 text-xs sm:text-sm px-2 sm:px-3"
                >
                  <span className="hidden sm:inline">Exit Exam</span>
                  <span className="sm:hidden">Exit</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6">
        <div className="flex flex-col lg:grid lg:grid-cols-12 gap-4 sm:gap-6">
          {/* Question Navigation Sidebar - Mobile: Top, Desktop: Left */}
          <div className="lg:col-span-3 order-1 lg:order-1">
            <Card className="lg:sticky lg:top-24">
              <CardHeader className="pb-2 sm:pb-3">
                <CardTitle className="text-base sm:text-lg flex items-center gap-2">
                  <Target className="h-4 w-4 sm:h-5 sm:w-5" />
                  <span className="hidden sm:inline">Questions</span>
                  <span className="sm:hidden">Q&apos;s</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-8 sm:grid-cols-6 lg:grid-cols-5 gap-1.5 sm:gap-2">
                  {questions.map((_, index) => {
                    const status = getQuestionStatus(index);
                    return (
                      <button
                        key={index}
                        onClick={() => handleQuestionNavigation(index)}
                        className={`
                          w-8 h-8 sm:w-10 sm:h-10 rounded-lg text-xs sm:text-sm font-medium transition-all duration-200 touch-manipulation
                          ${index === currentQuestionIndex
                            ? 'ring-2 ring-blue-500 ring-offset-1 sm:ring-offset-2'
                            : ''
                          }
                          ${getQuestionStatusColor(index)} text-white
                          hover:scale-105 hover:shadow-md active:scale-95
                        `}
                        title={`Question ${index + 1}${status.flagged ? ' (Flagged)' : ''}${status.answered ? ` (${status.correct ? 'Correct' : 'Incorrect'})` : ''}`}
                      >
                        {index + 1}
                      </button>
                    );
                  })}
                </div>

                {/* Legend - Hidden on mobile, shown on larger screens */}
                <div className="hidden sm:block mt-4 space-y-2 text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded bg-green-500"></div>
                    <span>Correct</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded bg-red-500"></div>
                    <span>Incorrect</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded bg-blue-500"></div>
                    <span>Answered</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded bg-yellow-500"></div>
                    <span>Flagged</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 rounded bg-gray-400"></div>
                    <span>Skipped</span>
                  </div>
                </div>

                {/* Progress */}
                <div className="mt-3 sm:mt-4">
                  <div className="flex items-center justify-between text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <span>Progress</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="h-1.5 sm:h-2" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Question Area */}
          <div className="lg:col-span-9 order-2 lg:order-2">
            <Card className="shadow-lg">
              <CardHeader className="pb-3 sm:pb-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4">
                  <CardTitle className="text-lg sm:text-xl">
                    Question {currentQuestionIndex + 1}
                  </CardTitle>
                  <div className="flex items-center gap-2">
                    {currentQuestion.topic && (
                      <Badge variant="secondary" className="text-xs">
                        {currentQuestion.topic}
                      </Badge>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleFlag}
                      className={`p-2 ${
                        flaggedQuestions.has(currentQuestionIndex)
                          ? 'text-yellow-600 bg-yellow-50'
                          : 'text-gray-400'
                      }`}
                    >
                      <Flag className="h-3 w-3 sm:h-4 sm:w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4 sm:space-y-6">
                <p className="text-base sm:text-lg text-gray-900 dark:text-white leading-relaxed">
                  {currentQuestion.questionText}
                </p>

                {/* Answer Choices */}
                <div className="space-y-3 sm:space-y-4">
                  {(['A', 'B', 'C', 'D'] as const).map((choice) => (
                    <div key={choice} className="space-y-2 sm:space-y-3">
                      <button
                        onClick={() => handleAnswerSelect(choice)}
                        disabled={!!currentQuestionAnswer || !!currentQuestion.userAnswer || (isPracticeMode && showFeedback)}
                        className={`group w-full text-left p-3 sm:p-4 rounded-xl transition-all duration-500 hover:shadow-md touch-manipulation ${getChoiceStyle(choice)} disabled:cursor-not-allowed ${
                          showFeedback && isPracticeMode && (choice === currentQuestion.correctAnswer || (selectedAnswer === choice && choice !== currentQuestion.correctAnswer))
                            ? 'transform scale-[1.02]'
                            : ''
                        }`}
                      >
                        <div className="flex items-start space-x-3 sm:space-x-4">
                          <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-xl flex items-center justify-center text-xs sm:text-sm font-bold flex-shrink-0 transition-all duration-300 ${
                            showFeedback && isPracticeMode && choice === currentQuestion.correctAnswer
                              ? 'bg-green-500 text-white shadow-lg shadow-green-500/30 ring-2 ring-green-300'
                              : showFeedback && isPracticeMode && selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                              ? 'bg-red-500 text-white shadow-lg shadow-red-500/30 ring-2 ring-red-300'
                              : selectedAnswer === choice || currentQuestionAnswer === choice || currentQuestion.userAnswer === choice
                              ? isPracticeMode
                                ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/30'
                                : 'bg-blue-500 text-white shadow-lg shadow-blue-500/30'
                              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 group-hover:bg-gray-200 dark:group-hover:bg-gray-600'
                          }`}>
                            {choice}
                          </div>
                          <span className="flex-1 text-sm sm:text-base text-gray-900 dark:text-white leading-relaxed">
                            {currentQuestion[`choice${choice}` as keyof ExamQuestion] as string}
                          </span>
                          {showFeedback && isPracticeMode && choice === currentQuestion.correctAnswer && (
                            <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600 flex-shrink-0" />
                          )}
                          {showFeedback && isPracticeMode && selectedAnswer === choice && choice !== currentQuestion.correctAnswer && (
                            <XCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-600 flex-shrink-0" />
                          )}
                        </div>
                      </button>

                      {/* Individual Choice Explanation */}
                      {showExplanation && currentExplanation && showFeedback && isPracticeMode && (
                        <div className={`ml-14 p-5 rounded-xl border-l-4 shadow-sm ${
                          choice === currentQuestion.correctAnswer
                            ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-500 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-600'
                            : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                            ? 'bg-gradient-to-r from-red-50 to-pink-50 border-red-500 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-600'
                            : 'bg-gradient-to-r from-gray-50 to-slate-50 border-gray-400 dark:from-gray-800/50 dark:to-slate-800/50 dark:border-gray-600'
                        }`}>
                          <div className="flex items-start gap-4">
                            <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                              choice === currentQuestion.correctAnswer
                                ? 'bg-green-500 text-white shadow-lg shadow-green-500/30'
                                : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                                ? 'bg-red-500 text-white shadow-lg shadow-red-500/30'
                                : 'bg-gray-400 text-white shadow-lg shadow-gray-400/30'
                            }`}>
                              {choice === currentQuestion.correctAnswer ? (
                                <CheckCircle className="h-5 w-5" />
                              ) : selectedAnswer === choice && choice !== currentQuestion.correctAnswer ? (
                                <XCircle className="h-5 w-5" />
                              ) : (
                                <span className="text-sm font-bold">{choice}</span>
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-3">
                                <h4 className={`text-sm font-semibold ${
                                  choice === currentQuestion.correctAnswer
                                    ? 'text-green-800 dark:text-green-200'
                                    : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                                    ? 'text-red-800 dark:text-red-200'
                                    : 'text-gray-700 dark:text-gray-300'
                                }`}>
                                  {choice === currentQuestion.correctAnswer
                                    ? '✓ Correct Answer'
                                    : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                                    ? '✗ Your Answer (Incorrect)'
                                    : 'Alternative Option'
                                  }
                                </h4>
                                {choice === currentQuestion.correctAnswer && (
                                  <Badge className="bg-green-100 text-green-800 text-xs">
                                    Best Choice
                                  </Badge>
                                )}
                                {selectedAnswer === choice && choice !== currentQuestion.correctAnswer && (
                                  <Badge variant="destructive" className="text-xs">
                                    Your Selection
                                  </Badge>
                                )}
                              </div>
                              <div className={`text-sm leading-relaxed space-y-2 ${
                                choice === currentQuestion.correctAnswer
                                  ? 'text-green-700 dark:text-green-300'
                                  : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                                  ? 'text-red-700 dark:text-red-300'
                                  : 'text-gray-600 dark:text-gray-400'
                              }`}>
                                <p className="font-medium">
                                  {choice === currentQuestion.correctAnswer
                                    ? 'Why this is correct:'
                                    : selectedAnswer === choice && choice !== currentQuestion.correctAnswer
                                    ? 'Why this is incorrect:'
                                    : 'Analysis:'
                                  }
                                </p>
                                <p>{getChoiceExplanation(choice)}</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {/* Practice Mode Feedback */}
                {showFeedback && isPracticeMode && (
                  <div className="space-y-3 sm:space-y-4">
                    {/* Result Summary */}
                    <div className={`p-3 sm:p-4 rounded-xl border-2 ${
                      currentQuestionCorrect
                        ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                        : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                    }`}>
                      <div className="flex items-center space-x-2 sm:space-x-3">
                        {currentQuestionCorrect ? (
                          <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 flex-shrink-0" />
                        ) : (
                          <XCircle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600 flex-shrink-0" />
                        )}
                        <div>
                          <h3 className={`text-base sm:text-lg font-semibold ${
                            currentQuestionCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
                          }`}>
                            {currentQuestionCorrect ? 'Correct!' : 'Incorrect'}
                          </h3>
                          <p className={`text-xs sm:text-sm ${
                            currentQuestionCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
                          }`}>
                            {currentQuestionCorrect
                              ? 'Well done! You selected the correct answer.'
                              : `The correct answer is ${currentQuestion.correctAnswer}.`
                            }
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3 justify-center">
                      {!currentQuestionCorrect && (
                        <Button
                          onClick={handleExplainQuestion}
                          disabled={isLoadingExplanation}
                          className="bg-blue-600 hover:bg-blue-700 text-white text-sm w-full sm:w-auto"
                          size="sm"
                        >
                          <BookOpen className="h-4 w-4 mr-2" />
                          {isLoadingExplanation ? 'Loading...' : 'Explain Answers'}
                        </Button>
                      )}

                      <Button
                        onClick={() => setShowChat(true)}
                        variant="outline"
                        className="border-purple-300 text-purple-700 hover:bg-purple-50 text-sm w-full sm:w-auto"
                        size="sm"
                      >
                        <MessageCircle className="h-4 w-4 mr-2" />
                        <span className="hidden sm:inline">Ask AI Tutor</span>
                        <span className="sm:hidden">AI Tutor</span>
                      </Button>

                      {currentQuestionIndex < questions.length - 1 ? (
                        <Button
                          onClick={handleNextQuestion}
                          className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white px-4 sm:px-6 py-2 shadow-lg text-sm w-full sm:w-auto"
                        >
                          <span className="hidden sm:inline">Continue to Next Question</span>
                          <span className="sm:hidden">Next Question</span>
                        </Button>
                      ) : (
                        <Button
                          onClick={onCompleteExam}
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white px-4 sm:px-6 py-2 shadow-lg text-sm w-full sm:w-auto"
                        >
                          Complete Exam
                        </Button>
                      )}
                    </div>
                  </div>
                )}

                {/* Navigation */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 bg-gray-50 dark:bg-gray-800/50 rounded-2xl p-3 sm:p-4">
                  <Button
                    variant="outline"
                    onClick={handlePreviousQuestion}
                    disabled={currentQuestionIndex === 0}
                    className="px-4 sm:px-6 py-2 disabled:opacity-50 text-sm w-full sm:w-auto"
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>

                  <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3">
                    {canSubmit && (
                      <Button
                        onClick={handleSubmitAnswer}
                        disabled={isSubmitting}
                        className={`px-4 sm:px-6 py-2 font-medium shadow-lg transition-all duration-300 text-sm w-full sm:w-auto ${
                          isPracticeMode
                            ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white'
                            : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white'
                        }`}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            <span className="hidden sm:inline">Submitting...</span>
                            <span className="sm:hidden">...</span>
                          </>
                        ) : (
                          <>
                            <span className="hidden sm:inline">Submit Answer</span>
                            <span className="sm:hidden">Submit</span>
                          </>
                        )}
                      </Button>
                    )}

                    {canProceed && currentQuestionIndex < questions.length - 1 && !showFeedback && (
                      <Button
                        onClick={handleNextQuestion}
                        className="px-4 sm:px-6 py-2 bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-200 text-sm w-full sm:w-auto"
                      >
                        Next
                        <ChevronRight className="h-4 w-4 ml-2" />
                      </Button>
                    )}

                    {canProceed && currentQuestionIndex === questions.length - 1 && !showFeedback && (
                      <Button
                        onClick={onCompleteExam}
                        className="px-4 sm:px-6 py-2 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg text-sm w-full sm:w-auto"
                      >
                        <span className="hidden sm:inline">Complete Exam</span>
                        <span className="sm:hidden">Complete</span>
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>


        </div>
      </div>

      {/* Chat Modal */}
      {showChat && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-2 sm:p-4">
          <div className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl w-full max-w-2xl h-[90vh] sm:h-[85vh] flex flex-col">
            {/* Chat Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-2 sm:gap-3">
                <div className="w-8 h-8 sm:w-10 sm:h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                  <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">AI Tutor</h3>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Question {currentQuestionIndex + 1} Support</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowChat(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Chat Messages */}
            <ScrollArea className="flex-1 p-6 overflow-y-auto">
              <div className="space-y-4 min-h-0">
                {chatMessages.length === 0 && (
                  <div className="text-center text-gray-500 dark:text-gray-400 py-12">
                    <div className="w-16 h-16 rounded-full bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 flex items-center justify-center mx-auto mb-4">
                      <HelpCircle className="h-8 w-8 text-purple-500" />
                    </div>
                    <h4 className="text-lg font-medium mb-2">CIPP/E Exam Tutor Ready!</h4>
                    <p className="text-sm">Ask me about GDPR concepts, legal principles, or anything related to this question.</p>
                    <p className="text-xs mt-2 text-gray-400">I&apos;ll give short, exam-focused explanations without revealing the answer.</p>
                  </div>
                )}
                {chatMessages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    <div className={`flex items-start gap-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                        message.role === 'user'
                          ? 'bg-blue-500 text-white'
                          : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white'
                      }`}>
                        {message.role === 'user' ? '👤' : '🤖'}
                      </div>
                      <div
                        className={`p-4 rounded-2xl text-sm leading-relaxed ${
                          message.role === 'user'
                            ? 'bg-blue-500 text-white rounded-br-md'
                            : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-md'
                        }`}
                      >
                        {message.role === 'assistant' ? (
                          <div className="space-y-3">
                            {message.content.split('\n\n').map((paragraph, index) => {
                              // Check if this is a list item
                              if (paragraph.match(/^(\d+\.|•|\*)/)) {
                                return (
                                  <div key={index} className="ml-4">
                                    {paragraph.split('\n').map((line, lineIndex) => (
                                      <div key={lineIndex} className="flex items-start gap-2 mb-1">
                                        {lineIndex === 0 && line.match(/^(\d+\.|•|\*)/) && (
                                          <span className="text-blue-500 font-medium mt-0.5">
                                            {line.match(/^(\d+\.|•|\*)/)?.[0]}
                                          </span>
                                        )}
                                        <span className="leading-relaxed">
                                          {line.replace(/^(\d+\.|•|\*)\s*/, '')}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                );
                              }

                              // Regular paragraph
                              return (
                                <p key={index} className="leading-relaxed">
                                  {paragraph.split('\n').map((line, lineIndex) => (
                                    <span key={lineIndex}>
                                      {line}
                                      {lineIndex < paragraph.split('\n').length - 1 && <br />}
                                    </span>
                                  ))}
                                </p>
                              );
                            })}
                          </div>
                        ) : (
                          message.content
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                {isSendingMessage && (
                  <div className="flex justify-start">
                    <div className="flex items-start gap-3">
                      <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                        🤖
                      </div>
                      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-2xl rounded-bl-md">
                        <div className="flex items-center space-x-2">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                          <span className="text-sm text-gray-600 dark:text-gray-400">AI is thinking...</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Chat Input */}
            <div className="p-6 border-t border-gray-200 dark:border-gray-700">
              <div className="flex gap-3">
                <Input
                  value={chatInput}
                  onChange={(e) => setChatInput(e.target.value)}
                  placeholder="Ask a question about this topic..."
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendChatMessage()}
                  disabled={isSendingMessage}
                  className="flex-1 rounded-xl border-gray-300 dark:border-gray-600 focus:border-purple-500 focus:ring-purple-500"
                />
                <Button
                  onClick={handleSendChatMessage}
                  disabled={!chatInput.trim() || isSendingMessage}
                  className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 text-white rounded-xl px-6"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
                Press Enter to send • AI gives short, exam-focused explanations • Ask follow-up questions anytime
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

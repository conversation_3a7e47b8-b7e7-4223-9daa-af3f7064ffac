"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
// Removed unused UI components
import { Slider } from "@/components/ui/slider";
import {

  FileText,
  Play,
  Target,
  BookOpen,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Loader2,
  BarChart3,
  Calendar
} from "lucide-react";

interface ExamSetupProps {
  certificate: Certificate;
  onStartExam: (examType: 'practice' | 'normal', questionCount: number) => void;
  onViewAnalytics: () => void;
}

export default function ExamSetup({ certificate, onStartExam, onViewAnalytics }: ExamSetupProps) {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedMode, setSelectedMode] = useState<'practice' | 'normal'>('practice');
  const [questionCount, setQuestionCount] = useState([20]);
  
  const { user } = useAuth();
  const { toast } = useToast();

  const loadQuestions = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const certificateQuestions = await getCertificateQuestions(user.uid, certificate.id);
      setQuestions(certificateQuestions);

      // Set default question count to min of 20 or total available
      const defaultCount = Math.min(20, certificateQuestions.length);
      setQuestionCount([defaultCount]);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast({
        title: "Error",
        description: "Failed to load questions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    if (user?.uid && certificate.id) {
      loadQuestions();
    }
  }, [user?.uid, certificate.id, loadQuestions]);

  const handleStartExam = () => {
    if (questions.length === 0) {
      toast({
        title: "No Questions Available",
        description: "Please add questions to the question bank first.",
        variant: "destructive",
      });
      return;
    }

    if (questionCount[0] > questions.length) {
      toast({
        title: "Not Enough Questions",
        description: `You can only select up to ${questions.length} questions.`,
        variant: "destructive",
      });
      return;
    }

    onStartExam(selectedMode, questionCount[0]);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-20">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading exam setup...</p>
        </div>
      </div>
    );
  }

  const maxQuestions = Math.min(questions.length, 100); // Cap at 100 questions
  const taggedQuestions = questions.filter(q => q.category);
  const topicCounts = questions.reduce((acc, q) => {
    const topic = q.category || 'Untagged';
    acc[topic] = (acc[topic] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="max-w-5xl mx-auto space-y-6">
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-medium text-gray-900 dark:text-white mb-2">
          Setup Your Exam
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Choose your exam mode and customize your practice session
        </p>
      </div>

      {/* Question Bank Status */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-2xl p-6 border border-blue-100 dark:border-blue-900/50">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
              <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">Question Bank</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">Your available practice questions</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <FileText className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-xl font-medium text-gray-900 dark:text-white">
                  {questions.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Questions</div>
              </div>
            </div>
          </div>

          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <Target className="h-4 w-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-xl font-medium text-gray-900 dark:text-white">
                  {taggedQuestions.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">AI Tagged</div>
              </div>
            </div>
          </div>

          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-white/20">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                <BarChart3 className="h-4 w-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-xl font-medium text-gray-900 dark:text-white">
                  {Object.keys(topicCounts).length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Topics Covered</div>
              </div>
            </div>
          </div>
        </div>

        {questions.length === 0 && (
          <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-950/30 rounded-xl border border-amber-200 dark:border-amber-800/50">
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-5 w-5 text-amber-600 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div>
                <p className="text-amber-800 dark:text-amber-200 font-medium">No questions available</p>
                <p className="text-amber-700 dark:text-amber-300 text-sm mt-1">
                  Please add questions to the Question Bank first to start taking exams.
                </p>
              </div>
            </div>
          </div>
        )}

        {questions.length > 0 && (
          <div className="mt-6 flex items-center justify-between">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              Ready to start your exam with {questions.length} available questions
            </div>
            <Button
              onClick={onViewAnalytics}
              variant="ghost"
              size="sm"
              className="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"
            >
              <Calendar className="h-4 w-4 mr-2" />
              View Previous Attempts
            </Button>
          </div>
        )}
      </div>

      {/* Exam Mode Selection */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Practice Mode */}
        <div
          className={`group cursor-pointer transition-all duration-300 ${
            selectedMode === 'practice'
              ? 'scale-[1.02]'
              : 'hover:scale-[1.01]'
          }`}
          onClick={() => setSelectedMode('practice')}
        >
          <div className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
            selectedMode === 'practice'
              ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 dark:border-green-800/50 shadow-lg shadow-green-100/50 dark:shadow-green-900/20'
              : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 hover:border-green-200 dark:hover:border-green-800/50 hover:shadow-md'
          }`}>
            {selectedMode === 'practice' && (
              <div className="absolute -top-2 -right-2">
                <div className="bg-green-500 text-white p-1.5 rounded-full shadow-lg">
                  <CheckCircle className="h-4 w-4" />
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3 mb-4">
              <div className={`p-3 rounded-xl transition-colors ${
                selectedMode === 'practice'
                  ? 'bg-green-100 dark:bg-green-900/30'
                  : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-green-100 dark:group-hover:bg-green-900/30'
              }`}>
                <BookOpen className={`h-5 w-5 transition-colors ${
                  selectedMode === 'practice'
                    ? 'text-green-600 dark:text-green-400'
                    : 'text-gray-600 dark:text-gray-400 group-hover:text-green-600 dark:group-hover:text-green-400'
                }`} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Practice Mode</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Learn with immediate feedback</p>
              </div>
            </div>

            <div className="space-y-2">
              {[
                'Immediate answer feedback',
                'Explanation after each question',
                'No time pressure',
                'Perfect for learning'
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <div className={`w-1.5 h-1.5 rounded-full ${
                    selectedMode === 'practice' ? 'bg-green-500' : 'bg-gray-400'
                  }`} />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Normal Mode */}
        <div
          className={`group cursor-pointer transition-all duration-300 ${
            selectedMode === 'normal'
              ? 'scale-[1.02]'
              : 'hover:scale-[1.01]'
          }`}
          onClick={() => setSelectedMode('normal')}
        >
          <div className={`relative p-6 rounded-2xl border-2 transition-all duration-300 ${
            selectedMode === 'normal'
              ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 dark:border-blue-800/50 shadow-lg shadow-blue-100/50 dark:shadow-blue-900/20'
              : 'border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800/50 hover:border-blue-200 dark:hover:border-blue-800/50 hover:shadow-md'
          }`}>
            {selectedMode === 'normal' && (
              <div className="absolute -top-2 -right-2">
                <div className="bg-blue-500 text-white p-1.5 rounded-full shadow-lg">
                  <CheckCircle className="h-4 w-4" />
                </div>
              </div>
            )}

            <div className="flex items-center space-x-3 mb-4">
              <div className={`p-3 rounded-xl transition-colors ${
                selectedMode === 'normal'
                  ? 'bg-blue-100 dark:bg-blue-900/30'
                  : 'bg-gray-100 dark:bg-gray-700 group-hover:bg-blue-100 dark:group-hover:bg-blue-900/30'
              }`}>
                <Target className={`h-5 w-5 transition-colors ${
                  selectedMode === 'normal'
                    ? 'text-blue-600 dark:text-blue-400'
                    : 'text-gray-600 dark:text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400'
                }`} />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Normal Mode</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Real exam simulation</p>
              </div>
            </div>

            <div className="space-y-2">
              {[
                'Results shown at the end',
                'Timed exam experience',
                'Real exam simulation',
                'Detailed final report'
              ].map((feature, index) => (
                <div key={index} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                  <div className={`w-1.5 h-1.5 rounded-full ${
                    selectedMode === 'normal' ? 'bg-blue-500' : 'bg-gray-400'
                  }`} />
                  <span>{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Question Count Selection */}
      {questions.length > 0 && (
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 mb-6">
            <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-xl">
              <FileText className="h-5 w-5 text-gray-600 dark:text-gray-400" />
            </div>
            <div>
              <h3 className="font-medium text-gray-900 dark:text-white">Question Count</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Choose how many questions for your exam
              </p>
            </div>
          </div>

          <div className="space-y-6">
            {/* Live Preview */}
            <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`text-3xl font-medium ${
                    selectedMode === 'practice' ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'
                  }`}>
                    {questionCount[0]}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">Questions Selected</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">
                      {selectedMode === 'practice' ? 'Practice Mode' : 'Normal Mode'}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-medium text-gray-900 dark:text-white">
                    ~{Math.ceil(questionCount[0] * 1.5)} min
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Estimated time</div>
                </div>
              </div>

              {/* Topic Distribution Preview */}
              {Object.keys(topicCounts).length > 0 && (
                <div className="space-y-2">
                  <div className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Estimated topic distribution:
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(topicCounts).slice(0, 5).map(([topic, count]) => {
                      const estimatedQuestions = Math.max(1, Math.floor((questionCount[0] / Object.keys(topicCounts).length)));
                      return (
                        <div key={topic} className="flex items-center space-x-1 text-xs">
                          <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-purple-400" />
                          <span className="text-gray-600 dark:text-gray-400">
                            {topic.length > 15 ? topic.substring(0, 15) + '...' : topic}
                          </span>
                          <span className="text-gray-500 dark:text-gray-500">
                            (~{Math.min(estimatedQuestions, count)})
                          </span>
                        </div>
                      );
                    })}
                    {Object.keys(topicCounts).length > 5 && (
                      <span className="text-xs text-gray-500 dark:text-gray-500">
                        +{Object.keys(topicCounts).length - 5} more topics
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Slider */}
            <div className="space-y-4">
              <Slider
                value={questionCount}
                onValueChange={setQuestionCount}
                max={maxQuestions}
                min={1}
                step={1}
                className="w-full"
              />
              <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
                <span>1 question</span>
                <div className="flex space-x-4">
                  <button
                    onClick={() => setQuestionCount([10])}
                    className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    10
                  </button>
                  <button
                    onClick={() => setQuestionCount([20])}
                    className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    20
                  </button>
                  <button
                    onClick={() => setQuestionCount([50])}
                    className="px-2 py-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    disabled={maxQuestions < 50}
                  >
                    50
                  </button>
                </div>
                <span>{maxQuestions} questions</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
        <Button
          onClick={handleStartExam}
          disabled={questions.length === 0}
          size="lg"
          className={`group relative overflow-hidden px-8 py-3 ${
            selectedMode === 'practice'
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 shadow-lg shadow-green-500/25'
              : 'bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 shadow-lg shadow-blue-500/25'
          } disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
        >
          <div className="flex items-center space-x-2">
            <Play className="h-5 w-5 transition-transform group-hover:scale-110" />
            <span className="font-medium">
              Start {selectedMode === 'practice' ? 'Practice' : 'Normal'} Exam
            </span>
          </div>
          <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12" />
        </Button>

        <Button
          onClick={onViewAnalytics}
          variant="outline"
          size="lg"
          className="group px-8 py-3 border-2 hover:bg-gray-50 dark:hover:bg-gray-800 transition-all duration-300"
        >
          <TrendingUp className="h-5 w-5 mr-2 transition-transform group-hover:scale-110" />
          <span className="font-medium">View Analytics</span>
        </Button>
      </div>
    </div>
  );
}

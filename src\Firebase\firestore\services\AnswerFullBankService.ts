import {
  collection,
  doc,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

export interface AnswerFullBankAttempt {
  id?: string;
  userId: string;
  certificateId: string;
  status: 'in_progress' | 'completed' | 'abandoned';
  startedAt: Timestamp;
  completedAt?: Timestamp;
  totalQuestions: number;
  answeredQuestions: number;
  correctAnswers: number;
  incorrectAnswers: number;
  totalTimeSpent: number; // in seconds
  currentQuestionIndex: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface AnswerFullBankQuestion {
  id?: string;
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  userAnswer?: 'A' | 'B' | 'C' | 'D';
  isCorrect?: boolean;
  timeSpent?: number; // in seconds
  topic?: string;
  explanation?: string;
  orderIndex: number;
  answeredAt?: Timestamp;
}

const ATTEMPTS_COLLECTION = 'answerFullBankAttempts';
const QUESTIONS_SUBCOLLECTION = 'questions';

/**
 * Create a new AnswerFull Question Bank attempt
 */
export const createAnswerFullBankAttempt = async (
  userId: string,
  certificateId: string,
  totalQuestions: number
): Promise<string> => {
  try {
    const attemptsRef = collection(firestore, ATTEMPTS_COLLECTION);
    
    const newAttempt: Omit<AnswerFullBankAttempt, 'id'> = {
      userId,
      certificateId,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      totalQuestions,
      answeredQuestions: 0,
      correctAnswers: 0,
      incorrectAnswers: 0,
      totalTimeSpent: 0,
      currentQuestionIndex: 0,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(attemptsRef, newAttempt);
    return docRef.id;
  } catch (error) {
    console.error('Error creating AnswerFull Bank attempt:', error);
    throw new Error('Failed to create AnswerFull Bank attempt');
  }
};

/**
 * Add questions to an AnswerFull Bank attempt
 */
export const addQuestionsToAnswerFullBankAttempt = async (
  attemptId: string,
  questions: Array<{
    questionId: string;
    questionText: string;
    choiceA: string;
    choiceB: string;
    choiceC: string;
    choiceD: string;
    correctAnswer: 'A' | 'B' | 'C' | 'D';
    topic?: string;
    explanation?: string;
  }>
): Promise<void> => {
  try {
    const batch = writeBatch(firestore);
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);

    questions.forEach((question, index) => {
      const questionRef = doc(questionsRef);
      const questionData: Omit<AnswerFullBankQuestion, 'id'> = {
        questionId: question.questionId,
        questionText: question.questionText,
        choiceA: question.choiceA,
        choiceB: question.choiceB,
        choiceC: question.choiceC,
        choiceD: question.choiceD,
        correctAnswer: question.correctAnswer,
        orderIndex: index,
      };

      // Only add optional fields if they have values
      if (question.topic) {
        questionData.topic = question.topic;
      }
      if (question.explanation) {
        questionData.explanation = question.explanation;
      }

      batch.set(questionRef, questionData);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error adding questions to AnswerFull Bank attempt:', error);
    throw new Error('Failed to add questions to AnswerFull Bank attempt');
  }
};

/**
 * Submit answer for a question in AnswerFull Bank
 */
export const submitAnswerFullBankAnswer = async (
  attemptId: string,
  questionId: string,
  userAnswer: 'A' | 'B' | 'C' | 'D',
  timeSpent: number
): Promise<boolean> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    
    // Find the question document
    const questionsQuery = query(questionsRef, where('questionId', '==', questionId));
    const questionsSnap = await getDocs(questionsQuery);
    
    if (questionsSnap.empty) {
      throw new Error('Question not found in AnswerFull Bank attempt');
    }

    const questionDoc = questionsSnap.docs[0];
    const questionData = questionDoc.data() as AnswerFullBankQuestion;
    const isCorrect = userAnswer === questionData.correctAnswer;

    // Update the question with the answer
    await updateDoc(questionDoc.ref, {
      userAnswer,
      isCorrect,
      timeSpent,
      answeredAt: serverTimestamp(),
    });

    // Update the attempt statistics
    const attemptSnap = await getDoc(attemptRef);
    if (attemptSnap.exists()) {
      const attemptData = attemptSnap.data() as AnswerFullBankAttempt;
      await updateDoc(attemptRef, {
        answeredQuestions: attemptData.answeredQuestions + 1,
        correctAnswers: isCorrect ? attemptData.correctAnswers + 1 : attemptData.correctAnswers,
        incorrectAnswers: !isCorrect ? attemptData.incorrectAnswers + 1 : attemptData.incorrectAnswers,
        totalTimeSpent: attemptData.totalTimeSpent + timeSpent,
        updatedAt: serverTimestamp(),
      });
    }

    return isCorrect;
  } catch (error) {
    console.error('Error submitting AnswerFull Bank answer:', error);
    throw new Error('Failed to submit AnswerFull Bank answer');
  }
};

/**
 * Update current question index in attempt
 */
export const updateAnswerFullBankProgress = async (
  attemptId: string,
  currentQuestionIndex: number
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    await updateDoc(attemptRef, {
      currentQuestionIndex,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating AnswerFull Bank progress:', error);
    throw new Error('Failed to update AnswerFull Bank progress');
  }
};

/**
 * Complete an AnswerFull Bank attempt
 */
export const completeAnswerFullBankAttempt = async (
  attemptId: string
): Promise<AnswerFullBankAttempt> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);

    await updateDoc(attemptRef, {
      status: 'completed',
      completedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    const updatedSnap = await getDoc(attemptRef);
    if (!updatedSnap.exists()) {
      throw new Error('Failed to retrieve completed attempt');
    }

    return { id: updatedSnap.id, ...updatedSnap.data() } as AnswerFullBankAttempt;
  } catch (error) {
    console.error('Error completing AnswerFull Bank attempt:', error);
    throw new Error('Failed to complete AnswerFull Bank attempt');
  }
};

/**
 * Abandon an AnswerFull Bank attempt (mark as abandoned)
 */
export const abandonAnswerFullBankAttempt = async (
  attemptId: string
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);

    await updateDoc(attemptRef, {
      status: 'abandoned',
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error abandoning AnswerFull Bank attempt:', error);
    throw new Error('Failed to abandon AnswerFull Bank attempt');
  }
};

/**
 * Get user's AnswerFull Bank attempts for a certificate
 */
export const getUserAnswerFullBankAttempts = async (
  userId: string,
  certificateId: string
): Promise<AnswerFullBankAttempt[]> => {
  try {
    const attemptsRef = collection(firestore, ATTEMPTS_COLLECTION);
    const q = query(
      attemptsRef,
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as AnswerFullBankAttempt[];
  } catch (error) {
    console.error('Error fetching AnswerFull Bank attempts:', error);
    throw new Error('Failed to fetch AnswerFull Bank attempts');
  }
};

/**
 * Get the current in-progress attempt for a user and certificate
 */
export const getCurrentAnswerFullBankAttempt = async (
  userId: string,
  certificateId: string
): Promise<AnswerFullBankAttempt | null> => {
  try {
    const attemptsRef = collection(firestore, ATTEMPTS_COLLECTION);
    const q = query(
      attemptsRef,
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      where('status', '==', 'in_progress'),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    if (querySnapshot.empty) {
      return null;
    }

    const doc = querySnapshot.docs[0];
    return { id: doc.id, ...doc.data() } as AnswerFullBankAttempt;
  } catch (error) {
    console.error('Error fetching current AnswerFull Bank attempt:', error);
    throw new Error('Failed to fetch current AnswerFull Bank attempt');
  }
};

/**
 * Get a specific AnswerFull Bank attempt by ID
 */
export const getAnswerFullBankAttempt = async (
  attemptId: string
): Promise<AnswerFullBankAttempt | null> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const attemptSnap = await getDoc(attemptRef);

    if (!attemptSnap.exists()) {
      return null;
    }

    return { id: attemptSnap.id, ...attemptSnap.data() } as AnswerFullBankAttempt;
  } catch (error) {
    console.error('Error fetching AnswerFull Bank attempt:', error);
    throw new Error('Failed to fetch AnswerFull Bank attempt');
  }
};

/**
 * Get questions from an AnswerFull Bank attempt
 */
export const getAnswerFullBankQuestions = async (
  attemptId: string
): Promise<AnswerFullBankQuestion[]> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const q = query(questionsRef, orderBy('orderIndex', 'asc'));

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as AnswerFullBankQuestion[];
  } catch (error) {
    console.error('Error fetching AnswerFull Bank questions:', error);
    throw new Error('Failed to fetch AnswerFull Bank questions');
  }
};

/**
 * Get incorrectly answered questions from AnswerFull Bank attempts for repair center
 */
export const getIncorrectAnswerFullBankQuestions = async (
  userId: string,
  certificateId: string
): Promise<Array<{
  questionId: string;
  isCorrect: boolean;
  timeSpent?: number;
  source: 'answer_full_bank';
  attemptId: string;
}>> => {
  try {
    const attempts = await getUserAnswerFullBankAttempts(userId, certificateId);
    const incorrectQuestions = [];

    for (const attempt of attempts) {
      if (attempt.id) {
        const questions = await getAnswerFullBankQuestions(attempt.id);
        
        for (const question of questions) {
          if (question.userAnswer && !question.isCorrect) {
            incorrectQuestions.push({
              questionId: question.questionId,
              isCorrect: false,
              timeSpent: question.timeSpent,
              source: 'answer_full_bank' as const,
              attemptId: attempt.id
            });
          }
        }
      }
    }
    
    return incorrectQuestions;
  } catch (error) {
    console.error('Error fetching incorrect AnswerFull Bank questions:', error);
    throw new Error('Failed to fetch incorrect AnswerFull Bank questions');
  }
};

import { google } from '@ai-sdk/google';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Allow longer processing time for explanations
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

// Schema for individual choice explanation
const ChoiceExplanationSchema = z.object({
  choice: z.enum(['A', 'B', 'C', 'D']).describe('The answer choice'),
  explanation: z.string().describe('Detailed explanation for this specific choice'),
  isCorrect: z.boolean().describe('Whether this choice is correct'),
  reasoning: z.string().describe('The reasoning behind why this choice is correct or incorrect')
});

// Schema for question explanation response
const QuestionExplanationSchema = z.object({
  explanation: z.string().describe('Overall explanation of the question and topic'),
  choiceExplanations: z.array(ChoiceExplanationSchema).describe('Detailed explanation for each answer choice'),
  keyPoints: z.array(z.string()).describe('Key learning points from this question'),
  relatedConcepts: z.array(z.string()).describe('Related GDPR/privacy concepts to explore'),
  difficulty: z.enum(['Easy', 'Medium', 'Hard']).describe('Difficulty level of this question'),
  studyTips: z.array(z.string()).describe('Tips for understanding this topic better')
});

export async function POST(req: NextRequest) {
  try {
    // Extract data from request body
    const { question, choiceA, choiceB, choiceC, choiceD, topic, certificateName } = await req.json();

    // Validate required fields
    if (!question || !choiceA || !choiceB || !choiceC || !choiceD) {
      return new NextResponse('Question and all choices are required.', { status: 400 });
    }

    if (!certificateName || typeof certificateName !== 'string') {
      return new NextResponse('Certificate name is required.', { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return new NextResponse('Server configuration error: Missing API Key.', { status: 500 });
    }

    // Get the AI model - using gemini-2.0-flash-exp as it's more reliable
    const model = google('gemini-2.0-flash-exp');

    // Construct the expert prompt for GDPR/CIPP-E explanation
    const expertPrompt = `
You are a world-class GDPR (General Data Protection Regulation) and CIPP/E (Certified Information Privacy Professional/Europe) expert and educator.

Your task is to provide a comprehensive educational explanation for the following multiple-choice question WITHOUT revealing which answer is correct.

Question Details:
Topic: ${topic || 'GDPR/Privacy Law'}
Certificate: ${certificateName}

Question: "${question}"

Answer Choices:
A) ${choiceA}
B) ${choiceB}
C) ${choiceC}
D) ${choiceD}

IMPORTANT: Do NOT reveal which answer is correct. Instead, provide educational analysis of each choice.

Please provide:

1. **Overall Explanation**: A comprehensive overview of the legal concepts and principles this question tests.

2. **Choice-by-Choice Analysis**: For each answer choice (A, B, C, D), provide:
   - A detailed explanation of what this choice represents
   - The legal reasoning or principle behind this option
   - Why this choice might be considered (even if incorrect)
   - What legal frameworks, articles, or concepts apply
   - Practical implications of choosing this option
   - Common misconceptions or pitfalls related to this choice

3. **Key Learning Points**: The most important concepts students should understand.

4. **Related Concepts**: Connected GDPR/privacy law topics worth studying.

5. **Difficulty Assessment**: Rate as Easy, Medium, or Hard based on legal complexity.

6. **Study Tips**: Specific advice for mastering this topic area.

Focus on deep educational value. Each choice explanation should be substantial (3-4 sentences minimum) and help students understand the underlying legal principles, even for incorrect options.
`;

    // Call the AI model to generate structured response
    const result = await generateObject({
      model: model,
      prompt: expertPrompt,
      schema: QuestionExplanationSchema,
    });

    // Return the structured response
    return NextResponse.json({
      success: true,
      data: result.object
    });

  } catch (error) {
    console.error('Error generating question explanation:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return new NextResponse('API configuration error.', { status: 500 });
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return new NextResponse('Service temporarily unavailable. Please try again later.', { status: 503 });
      }
      if (error.message.includes('timeout')) {
        return new NextResponse('Request timeout. Please try again later.', { status: 408 });
      }
    }
    
    return new NextResponse('Failed to generate question explanation.', { status: 500 });
  }
}

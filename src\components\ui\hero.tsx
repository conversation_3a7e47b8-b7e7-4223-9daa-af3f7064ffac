"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Locale } from '@/i18n-config';

interface HeroProps {
  dictionary: {
    title?: string;
    subtitle?: string;
    upload?: string;
    process?: string;
    budget?: string;
    ready?: string;
  };
  lang: Locale;
}

const RotatingWord = ({ word, delay }: { word: string, delay: number }) => {
  return (
    <div className="relative inline-block mx-1">
      <motion.span
        className="inline-block font-bold text-primary"
        initial={{ rotateX: 90, opacity: 0 }}
        animate={{ rotateX: 0, opacity: 1 }}
        transition={{ duration: 0.6, delay, ease: "easeOut" }}
      >
        {word}
      </motion.span>
    </div>
  );
};

const ProcessStep = ({ icon, text, delay }: { icon: React.ReactNode, text: string, delay: number }) => {
  return (
    <motion.div 
      className="flex flex-col items-center justify-center p-4 bg-card rounded-lg shadow-md w-full md:w-[180px]"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.4, delay }}
    >
      <div className="text-4xl mb-2 text-primary">{icon}</div>
      <p className="text-sm text-center text-card-foreground">{text}</p>
    </motion.div>
  );
};

export default function Hero({ dictionary, lang }: HeroProps) {
  const processingRef = React.useRef<HTMLDivElement>(null);
  
  const defaultTexts = {
    title: lang === 'ar' ? 'مقترحات معدة بـ' : 'Proposals Made with',
    subtitle: lang === 'ar' 
      ? 'نحن متخصصون في التحول الرقمي وإدارة البيانات وهندسة المؤسسات'
      : 'We specialize in Digital Transformation, Data Management, and Enterprise Architecture',
    upload: lang === 'ar' ? 'تحميل RFP' : 'Upload RFP',
    process: lang === 'ar' ? 'معالجة ذكية' : 'AI Processing',
    budget: lang === 'ar' ? 'ميزانية تلقائية' : 'Auto Budget',
    ready: lang === 'ar' ? 'المقترح جاهز' : 'Proposal Ready',
  };

  const texts = { ...defaultTexts, ...dictionary };
  
  const rtl = lang === 'ar';
  
  return (
    <div className={`w-full min-h-[calc(100vh-2rem)] bg-background ${rtl ? 'rtl' : 'ltr'}`}>
      <div className="container mx-auto px-4 py-12">
        {/* Header with rotating words */}
        <div className="mb-16 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            {texts.title}{' '}
            <RotatingWord word={lang === 'ar' ? 'سهولة' : 'Ease'} delay={0.2} />
            <RotatingWord word={lang === 'ar' ? 'سرعة' : 'Speed'} delay={0.5} />
            <RotatingWord word={lang === 'ar' ? 'دقة' : 'Precision'} delay={0.8} />
          </h1>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            {texts.subtitle}
          </p>
        </div>
        
        {/* Animation Section */}
        <div className="relative max-w-4xl mx-auto">
          {/* Animation Flow */}
          <div className="flex flex-col md:flex-row justify-between items-center md:items-start gap-4 md:gap-8 mb-12 relative">
            {/* Connection Lines in Background */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-accent hidden md:block" />
            
            {/* Process Steps */}
            <ProcessStep 
              icon={<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" /><polyline points="17 8 12 3 7 8" /><line x1="12" y1="3" x2="12" y2="15" /></svg>} 
              text={texts.upload} 
              delay={1.1} 
            />
            
            {/* Processing Machine */}
            <motion.div 
              className="w-full md:w-auto"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              ref={processingRef}
            >
              <div className="bg-secondary p-5 rounded-xl relative">
                <div className="absolute -top-3 left-3 w-4 h-4 bg-accent rounded-full" />
                <div className="absolute -top-3 right-3 w-4 h-4 bg-primary rounded-full" />
                <motion.div 
                  className="w-full h-24 flex items-center justify-center rounded-md overflow-hidden bg-secondary-foreground"
                  animate={{ 
                    background: ['hsl(var(--secondary-foreground))', 'hsl(var(--primary))', 'hsl(var(--secondary-foreground))'] 
                  }}
                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                >
                  <motion.div
                    className="text-3xl"
                    animate={{ 
                      opacity: [0, 1, 0],
                      scale: [0.8, 1.2, 0.8]
                    }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="3"></circle><path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path></svg>
                  </motion.div>
                </motion.div>
                <div className="mt-2 text-xs text-center text-secondary-foreground">
                  {texts.process}
                </div>
              </div>
            </motion.div>
            
            <ProcessStep 
              icon={<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><circle cx="12" cy="12" r="10"></circle><line x1="12" y1="8" x2="12" y2="12"></line><line x1="12" y1="16" x2="12.01" y2="16"></line></svg>} 
              text={texts.budget} 
              delay={1.7} 
            />
            
            <ProcessStep 
              icon={<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline></svg>} 
              text={texts.ready} 
              delay={2} 
            />
          </div>
          
          {/* Dashboard Preview */}
          <motion.div 
            className="bg-card rounded-xl shadow-lg overflow-hidden"
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.8, delay: 2.3 }}
          >
            <div className="h-8 bg-muted flex items-center px-4">
              <div className="flex space-x-2">
                <div className="w-3 h-3 rounded-full bg-destructive opacity-70" />
                <div className="w-3 h-3 rounded-full bg-chart-4 opacity-70" />
                <div className="w-3 h-3 rounded-full bg-chart-1 opacity-70" />
              </div>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <motion.div 
                  className="h-32 bg-accent/10 rounded-lg p-4"
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <div className="h-4 w-20 bg-accent/30 rounded mb-2" />
                  <div className="h-3 w-full bg-accent/20 rounded mb-2" />
                  <div className="h-3 w-3/4 bg-accent/20 rounded mb-2" />
                  <div className="h-3 w-1/2 bg-accent/20 rounded" />
                </motion.div>
                
                <motion.div 
                  className="h-32 bg-primary/10 rounded-lg p-4"
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <div className="h-4 w-20 bg-primary/30 rounded mb-2" />
                  <div className="h-3 w-full bg-primary/20 rounded mb-2" />
                  <div className="h-3 w-3/4 bg-primary/20 rounded mb-2" />
                  <div className="h-3 w-1/2 bg-primary/20 rounded" />
                </motion.div>
                
                <motion.div 
                  className="h-32 bg-secondary/10 rounded-lg p-4"
                  whileHover={{ y: -5, transition: { duration: 0.2 } }}
                >
                  <div className="h-4 w-20 bg-secondary/30 rounded mb-2" />
                  <div className="h-3 w-full bg-secondary/20 rounded mb-2" />
                  <div className="h-3 w-3/4 bg-secondary/20 rounded mb-2" />
                  <div className="h-3 w-1/2 bg-secondary/20 rounded" />
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
} 
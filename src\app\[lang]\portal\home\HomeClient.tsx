"use client";

import React from "react";
import { Dictionary } from "@/dictionaries";

interface HomeClientProps {
  dict: Dictionary;
}

export default function HomeClient({}: HomeClientProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 p-4 sm:p-6 lg:p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center py-8 sm:py-12 lg:py-20">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
            Welcome to Your Home Dashboard
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-gray-600 dark:text-gray-400 mb-6 sm:mb-8 px-4">
            Your certification journey starts here
          </p>
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 sm:p-8 shadow-xl">
            <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400">
              This is your home dashboard. Content will be added here soon.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

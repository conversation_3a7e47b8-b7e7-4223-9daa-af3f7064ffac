import { google } from '@ai-sdk/google';
import { generateText } from 'ai';
import { NextRequest, NextResponse } from 'next/server';

// Allow longer processing time for chat responses
export const maxDuration = 60;

// Basic error handling for missing environment variables
if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
  console.error('Missing GOOGLE_GENERATIVE_AI_API_KEY environment variable');
}

export async function POST(req: NextRequest) {
  try {
    console.log('QuestionChat API called');

    // Extract data from request body
    const {
      userMessage,
      questionContext,
      chatHistory,
      certificateName
    } = await req.json();

    console.log('Request data:', { userMessage, certificateName, hasQuestionContext: !!questionContext });

    // Validate required fields
    if (!userMessage || typeof userMessage !== 'string') {
      return new NextResponse('User message is required.', { status: 400 });
    }

    if (!questionContext) {
      return new NextResponse('Question context is required.', { status: 400 });
    }

    if (!certificateName || typeof certificateName !== 'string') {
      return new NextResponse('Certificate name is required.', { status: 400 });
    }

    if (!process.env.GOOGLE_GENERATIVE_AI_API_KEY) {
      return new NextResponse('Server configuration error: Missing API Key.', { status: 500 });
    }

    // Get the AI model - using gemini-2.0-flash-exp as it's more reliable
    const model = google('gemini-2.0-flash-exp');

    // Build chat history context
    const historyContext = chatHistory && chatHistory.length > 0 
      ? `\n\nPrevious conversation:\n${chatHistory.map((msg: { role: string; content: string }) =>
          `${msg.role === 'user' ? 'Student' : 'Expert'}: ${msg.content}`
        ).join('\n')}`
      : '';

    // Construct the expert prompt for GDPR/CIPP-E chat
    const expertPrompt = `
You are a CIPP/E exam tutor. A student is asking about this specific question:

"${questionContext.question}"

The answer choices are:
A) ${questionContext.choiceA}
B) ${questionContext.choiceB}
C) ${questionContext.choiceC}
D) ${questionContext.choiceD}

Student's specific question: "${userMessage}"

INSTRUCTIONS:
- Answer their SPECIFIC question about this scenario
- Do NOT reveal which choice is correct
- Keep response to 2-3 sentences maximum
- Focus on the legal concept they're asking about
- Use the actual details from the question scenario
- Be conversational and helpful
- Don't use template phrases like "For CIPP/E exam purposes" unless natural

EXAMPLE RESPONSES:
If asked "Why not A?": "Choice A suggests [specific concept]. However, in this scenario with [specific details from question], that approach might not address [specific issue]."

If asked "What is adequacy?": "In this context, adequacy refers to [concept as it applies to this specific scenario]. The key issue here is [specific detail from the question]."

${historyContext}

Respond directly to what they asked, using the specific context of this question. Be natural and specific, not generic.
`;

    console.log('Sending request to AI model...');

    // Enhance prompt based on question type
    let enhancedPrompt = expertPrompt;

    // If asking about a specific choice, add context
    if (userMessage.match(/why not [A-D]/i)) {
      const choice = userMessage.match(/why not ([A-D])/i)?.[1];
      if (choice) {
        enhancedPrompt += `\n\nThe student is specifically asking why choice ${choice} might not be correct. Focus on the legal reasoning around that specific option without revealing the right answer.`;
      }
    }

    // If asking about a concept, focus on that
    if (userMessage.toLowerCase().includes('what is') || userMessage.toLowerCase().includes('explain')) {
      enhancedPrompt += `\n\nThe student wants a concept explanation. Focus on the legal principle or GDPR concept they're asking about in the context of this specific scenario.`;
    }

    // If asking about differences between choices
    if (userMessage.toLowerCase().includes('difference') || userMessage.toLowerCase().includes('vs')) {
      enhancedPrompt += `\n\nThe student is asking about differences between options. Compare the legal concepts without revealing which is correct.`;
    }

    // Call the AI model to generate response
    const result = await generateText({
      model: model,
      prompt: enhancedPrompt,
      maxTokens: 250, // Even shorter for more focused responses
    });

    console.log('AI response received:', result.text?.substring(0, 100) + '...');

    // Return the response
    return NextResponse.json({
      success: true,
      message: result.text
    });

  } catch (error) {
    console.error('Error generating chat response:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('API key')) {
        return new NextResponse('API configuration error.', { status: 500 });
      }
      if (error.message.includes('quota') || error.message.includes('limit')) {
        return new NextResponse('Service temporarily unavailable. Please try again later.', { status: 503 });
      }
      if (error.message.includes('timeout')) {
        return new NextResponse('Request timeout. Please try again later.', { status: 408 });
      }
    }
    
    return new NextResponse('Failed to generate chat response.', { status: 500 });
  }
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import {
  createGameAttempt,
  addGameQuestionResult,
  completeGameAttempt
} from "@/Firebase/firestore/services/GameService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  Map,
  Crown,
  Shield,
  Sword,
  Clock,
  Star,
  Trophy,
  Loader2,
  Play,
  Target,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Lightbulb
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface DomainDominationGameProps {
  certificate: Certificate;
  onBack: () => void;
}

interface Player {
  name: string;
  isActive: boolean;
}

interface Territory {
  id: string;
  name: string;
  owner: 0 | 1 | null; // null = neutral
  isCapital: boolean;
  isFortified: boolean;
  questions: Question[];
  color: string;
}

interface ChoiceExplanation {
  choice: 'A' | 'B' | 'C' | 'D';
  explanation: string;
  isCorrect: boolean;
  reasoning: string;
}

interface QuestionExplanationData {
  explanation: string;
  choiceExplanations: ChoiceExplanation[];
  keyPoints: string[];
  relatedConcepts: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  studyTips: string[];
}

interface GameState {
  phase: 'setup' | 'action' | 'question' | 'result' | 'gameOver';
  currentTurn: 0 | 1;
  players: [Player, Player];
  territories: Territory[];
  selectedAction: 'capture' | 'invade' | 'fortify' | null;
  selectedTerritory: string | null;
  currentQuestion: Question | null;
  currentQuestions: Question[]; // For multi-question challenges
  questionsAnswered: number;
  questionsRequired: number;
  playerAnswer: string | null;
  correctAnswer: string | null;
  questionExplanation: QuestionExplanationData | null;
  isLoadingExplanation: boolean;
  timeRemaining: number;
  gameWinner: 0 | 1 | null;
  gameAttemptId: string | null;
  actionStartTime: number | null;
  usedQuestionIds: Set<string>;
  turnNumber: number;
}

// GDPR Domain categories with colors
const DOMAIN_CATEGORIES = [
  { name: "Data Protection Principles", color: "bg-blue-500", isCapital: true },
  { name: "Data Subject Rights", color: "bg-green-500", isCapital: true },
  { name: "Legal Basis for Processing", color: "bg-purple-500", isCapital: false },
  { name: "Data Controller & Processor", color: "bg-orange-500", isCapital: false },
  { name: "International Transfers", color: "bg-red-500", isCapital: false },
  { name: "Breach Notification", color: "bg-yellow-500", isCapital: false },
  { name: "Privacy by Design", color: "bg-indigo-500", isCapital: false },
  { name: "Consent Management", color: "bg-pink-500", isCapital: false }
];

export default function DomainDominationGame({ certificate, onBack }: DomainDominationGameProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [gameState, setGameState] = useState<GameState>({
    phase: 'setup',
    currentTurn: 0,
    players: [
      { name: '', isActive: false },
      { name: '', isActive: false }
    ],
    territories: [],
    selectedAction: null,
    selectedTerritory: null,
    currentQuestion: null,
    currentQuestions: [],
    questionsAnswered: 0,
    questionsRequired: 1,
    playerAnswer: null,
    correctAnswer: null,
    questionExplanation: null,
    isLoadingExplanation: false,
    timeRemaining: 60,
    gameWinner: null,
    gameAttemptId: null,
    actionStartTime: null,
    usedQuestionIds: new Set(),
    turnNumber: 1
  });

  const [playerInputs, setPlayerInputs] = useState({
    player1Name: '',
    player2Name: ''
  });

  const [currentAnswer, setCurrentAnswer] = useState<string>('');
  const [showAdditionalLearning, setShowAdditionalLearning] = useState<boolean>(false);

  const loadQuestions = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const fetchedQuestions = await getCertificateQuestions(user.uid, certificate.id);

      if (fetchedQuestions.length < 20) {
        toast({
          title: "Insufficient Questions",
          description: `You need at least 20 questions to play Domain Domination. You currently have ${fetchedQuestions.length} questions.`,
          variant: "destructive",
        });
        return;
      }

      // Shuffle questions for random selection
      const shuffled = [...fetchedQuestions].sort(() => Math.random() - 0.5);
      setQuestions(shuffled);
      
      // Initialize territories with questions
      initializeTerritories(shuffled);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast({
        title: "Error",
        description: "Failed to load questions for the game.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    loadQuestions();
  }, [loadQuestions]);

  const initializeTerritories = (allQuestions: Question[]) => {
    const territories: Territory[] = DOMAIN_CATEGORIES.map((category, index) => {
      // Filter questions by category/tags or distribute evenly
      const categoryQuestions = allQuestions.filter(q => 
        q.category?.toLowerCase().includes(category.name.toLowerCase()) ||
        q.tags?.some(tag => tag.toLowerCase().includes(category.name.toLowerCase()))
      );
      
      // If no specific questions found, distribute remaining questions
      const questionsForTerritory = categoryQuestions.length > 0 
        ? categoryQuestions.slice(0, 10)
        : allQuestions.slice(index * 10, (index + 1) * 10);

      return {
        id: `territory-${index}`,
        name: category.name,
        owner: null,
        isCapital: category.isCapital,
        isFortified: false,
        questions: questionsForTerritory,
        color: category.color
      };
    });

    setGameState(prev => ({ ...prev, territories }));
  };

  const fetchQuestionExplanation = useCallback(async (question: Question) => {
    try {
      setGameState(prev => ({ ...prev, isLoadingExplanation: true }));

      const response = await fetch('/api/QuestionExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.question,
          choiceA: question.choiceA,
          choiceB: question.choiceB,
          choiceC: question.choiceC,
          choiceD: question.choiceD,
          topic: question.category || 'GDPR',
          certificateName: certificate.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch explanation');
      }

      const result = await response.json();

      if (result.success && result.data) {
        setGameState(prev => ({
          ...prev,
          questionExplanation: result.data,
          isLoadingExplanation: false
        }));
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching question explanation:', error);
      setGameState(prev => ({ ...prev, isLoadingExplanation: false }));
      toast({
        title: "Explanation Error",
        description: "Could not load detailed explanation. Basic explanation will be shown.",
        variant: "destructive",
      });
    }
  }, [certificate.name, toast]);

  const handleTimeoutAnswer = useCallback(async () => {
    // Player loses their turn due to timeout
    const timeSpent = gameState.actionStartTime ? Math.floor((Date.now() - gameState.actionStartTime) / 1000) : 60;

    // Track the failed attempt
    if (gameState.gameAttemptId && gameState.currentQuestion) {
      try {
        await addGameQuestionResult(gameState.gameAttemptId, {
          questionId: gameState.currentQuestion.id!,
          questionText: gameState.currentQuestion.question,
          choiceA: gameState.currentQuestion.choiceA,
          choiceB: gameState.currentQuestion.choiceB,
          choiceC: gameState.currentQuestion.choiceC,
          choiceD: gameState.currentQuestion.choiceD,
          correctAnswer: gameState.currentQuestion.correctAnswer,
          roundNumber: gameState.turnNumber,
          highBidder: gameState.currentTurn,
          highBidderWager: 0,
          highBidderAnswer: undefined,
          highBidderCorrect: false,
          roundWinner: null,
          timeSpentAnswer: timeSpent,
          topic: gameState.currentQuestion.category || undefined,
          explanation: gameState.currentQuestion.explanation || undefined
        });
      } catch (error) {
        console.error('Error tracking question result:', error);
      }
    }

    setGameState(prev => ({
      ...prev,
      phase: 'result',
      correctAnswer: prev.currentQuestion?.correctAnswer || null,
      playerAnswer: null
    }));

    // Fetch detailed explanation for the result phase
    if (gameState.currentQuestion) {
      await fetchQuestionExplanation(gameState.currentQuestion);
    }

    toast({
      title: "Time's Up!",
      description: `${gameState.players[gameState.currentTurn].name} ran out of time and failed the action.`,
      variant: "destructive",
    });

    // Additional notification for repair center
    toast({
      title: "Added to Repair Center",
      description: "This timed-out question has been added to your repair center for review.",
      variant: "default",
    });
  }, [gameState.gameAttemptId, gameState.currentQuestion, gameState.actionStartTime, gameState.currentTurn, gameState.players, gameState.turnNumber, toast, fetchQuestionExplanation]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (gameState.phase === 'question' && gameState.timeRemaining > 0) {
      interval = setInterval(() => {
        setGameState(prev => ({
          ...prev,
          timeRemaining: prev.timeRemaining - 1
        }));
      }, 1000);
    }

    // Auto-submit when time runs out
    if (gameState.timeRemaining === 0 && gameState.phase === 'question') {
      handleTimeoutAnswer();
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [gameState.phase, gameState.timeRemaining, handleTimeoutAnswer]);

  const startGame = async () => {
    if (!playerInputs.player1Name.trim() || !playerInputs.player2Name.trim()) {
      toast({
        title: "Player Names Required",
        description: "Please enter names for both players.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Create game attempt in Firebase
      const gameAttemptId = await createGameAttempt(
        user!.uid,
        certificate.id!,
        'domain-domination',
        playerInputs.player1Name.trim(),
        playerInputs.player2Name.trim(),
        50 // Estimated max turns
      );

      setGameState(prev => ({
        ...prev,
        phase: 'action',
        gameAttemptId,
        players: [
          { name: playerInputs.player1Name.trim(), isActive: true },
          { name: playerInputs.player2Name.trim(), isActive: false }
        ]
      }));
    } catch (error) {
      console.error('Error starting game:', error);
      toast({
        title: "Error",
        description: "Failed to start game. Please try again.",
        variant: "destructive",
      });
    }
  };

  const selectAction = (action: 'capture' | 'invade' | 'fortify') => {
    setGameState(prev => ({
      ...prev,
      selectedAction: action,
      selectedTerritory: null
    }));
  };

  const selectTerritory = (territoryId: string) => {
    const territory = gameState.territories.find(t => t.id === territoryId);
    if (!territory) return;

    const action = gameState.selectedAction;
    if (!action) return;

    // Validate action
    if (action === 'capture' && territory.owner !== null) {
      toast({
        title: "Invalid Action",
        description: "You can only capture neutral territories.",
        variant: "destructive",
      });
      return;
    }

    if (action === 'invade' && territory.owner === gameState.currentTurn) {
      toast({
        title: "Invalid Action",
        description: "You cannot invade your own territory.",
        variant: "destructive",
      });
      return;
    }

    if (action === 'invade' && territory.owner === null) {
      toast({
        title: "Invalid Action",
        description: "You cannot invade neutral territories. Use CAPTURE instead.",
        variant: "destructive",
      });
      return;
    }

    if (action === 'fortify' && territory.owner !== gameState.currentTurn) {
      toast({
        title: "Invalid Action",
        description: "You can only fortify your own territories.",
        variant: "destructive",
      });
      return;
    }

    // Set up the challenge
    const questionsRequired = action === 'capture' ? 1 :
                             action === 'invade' ? (territory.isFortified ? 3 : 2) : 1;

    const availableQuestions = territory.questions.filter(q => !gameState.usedQuestionIds.has(q.id!));

    if (availableQuestions.length < questionsRequired) {
      toast({
        title: "Insufficient Questions",
        description: `Not enough questions available for this territory. Need ${questionsRequired}, have ${availableQuestions.length}.`,
        variant: "destructive",
      });
      return;
    }

    const selectedQuestions = availableQuestions.slice(0, questionsRequired);

    setGameState(prev => ({
      ...prev,
      phase: 'question',
      selectedTerritory: territoryId,
      currentQuestions: selectedQuestions,
      currentQuestion: selectedQuestions[0],
      questionsAnswered: 0,
      questionsRequired,
      timeRemaining: 60,
      actionStartTime: Date.now()
    }));
  };

  const submitAnswer = async () => {
    if (!currentAnswer) {
      toast({
        title: "Answer Required",
        description: "Please select an answer.",
        variant: "destructive",
      });
      return;
    }

    const isCorrect = currentAnswer === gameState.currentQuestion?.correctAnswer;
    const timeSpent = gameState.actionStartTime ? Math.floor((Date.now() - gameState.actionStartTime) / 1000) : 0;

    // Track the question result
    if (gameState.gameAttemptId && gameState.currentQuestion) {
      try {
        await addGameQuestionResult(gameState.gameAttemptId, {
          questionId: gameState.currentQuestion.id!,
          questionText: gameState.currentQuestion.question,
          choiceA: gameState.currentQuestion.choiceA,
          choiceB: gameState.currentQuestion.choiceB,
          choiceC: gameState.currentQuestion.choiceC,
          choiceD: gameState.currentQuestion.choiceD,
          correctAnswer: gameState.currentQuestion.correctAnswer,
          roundNumber: gameState.turnNumber,
          highBidder: gameState.currentTurn,
          highBidderWager: 0,
          highBidderAnswer: currentAnswer as 'A' | 'B' | 'C' | 'D',
          highBidderCorrect: isCorrect,
          roundWinner: isCorrect ? gameState.currentTurn : null,
          timeSpentAnswer: timeSpent,
          topic: gameState.currentQuestion.category || undefined,
          explanation: gameState.currentQuestion.explanation || undefined
        });
      } catch (error) {
        console.error('Error tracking question result:', error);
      }
    }

    // Add question to used set
    const newUsedQuestionIds = new Set(gameState.usedQuestionIds);
    newUsedQuestionIds.add(gameState.currentQuestion!.id!);

    if (!isCorrect) {
      // Failed the challenge
      // Notify that question was added to repair center
      toast({
        title: "Added to Repair Center",
        description: "This incorrect answer has been added to your repair center for review.",
        variant: "default",
      });

      setGameState(prev => ({
        ...prev,
        phase: 'result',
        correctAnswer: prev.currentQuestion?.correctAnswer || null,
        playerAnswer: currentAnswer,
        usedQuestionIds: newUsedQuestionIds
      }));

      // Fetch detailed explanation
      if (gameState.currentQuestion) {
        await fetchQuestionExplanation(gameState.currentQuestion);
      }
    } else {
      // Correct answer
      const newQuestionsAnswered = gameState.questionsAnswered + 1;

      if (newQuestionsAnswered >= gameState.questionsRequired) {
        // Challenge completed successfully
        executeAction();

        setGameState(prev => ({
          ...prev,
          phase: 'result',
          correctAnswer: prev.currentQuestion?.correctAnswer || null,
          playerAnswer: currentAnswer,
          usedQuestionIds: newUsedQuestionIds
        }));

        // Fetch detailed explanation
        if (gameState.currentQuestion) {
          await fetchQuestionExplanation(gameState.currentQuestion);
        }
      } else {
        // Move to next question
        const nextQuestion = gameState.currentQuestions[newQuestionsAnswered];
        setGameState(prev => ({
          ...prev,
          currentQuestion: nextQuestion,
          questionsAnswered: newQuestionsAnswered,
          timeRemaining: 60,
          actionStartTime: Date.now(),
          usedQuestionIds: newUsedQuestionIds
        }));
      }
    }

    setCurrentAnswer('');
  };

  const executeAction = () => {
    const territory = gameState.territories.find(t => t.id === gameState.selectedTerritory);
    if (!territory) return;

    const newTerritories = [...gameState.territories];
    const territoryIndex = newTerritories.findIndex(t => t.id === gameState.selectedTerritory);

    if (gameState.selectedAction === 'capture') {
      newTerritories[territoryIndex].owner = gameState.currentTurn;
      toast({
        title: "Territory Captured!",
        description: `${gameState.players[gameState.currentTurn].name} captured ${territory.name}!`,
      });
    } else if (gameState.selectedAction === 'invade') {
      newTerritories[territoryIndex].owner = gameState.currentTurn;
      newTerritories[territoryIndex].isFortified = false; // Remove fortification
      toast({
        title: "Territory Invaded!",
        description: `${gameState.players[gameState.currentTurn].name} invaded ${territory.name}!`,
      });
    } else if (gameState.selectedAction === 'fortify') {
      newTerritories[territoryIndex].isFortified = true;
      toast({
        title: "Territory Fortified!",
        description: `${territory.name} is now fortified and harder to invade!`,
      });
    }

    setGameState(prev => ({
      ...prev,
      territories: newTerritories
    }));

    // Check for victory conditions
    checkVictoryConditions(newTerritories);
  };

  const checkVictoryConditions = (territories: Territory[]) => {
    const player0Territories = territories.filter(t => t.owner === 0);
    const player1Territories = territories.filter(t => t.owner === 1);

    // Total domination
    if (player0Territories.length === territories.length) {
      endGame(0, "Total Domination");
      return;
    }
    if (player1Territories.length === territories.length) {
      endGame(1, "Total Domination");
      return;
    }

    // Capital control
    const player0Capitals = territories.filter(t => t.isCapital && t.owner === 0);
    const player1Capitals = territories.filter(t => t.isCapital && t.owner === 1);

    if (player0Capitals.length === 2) {
      endGame(0, "Capital Control");
      return;
    }
    if (player1Capitals.length === 2) {
      endGame(1, "Capital Control");
      return;
    }

    // Majority control (more than half)
    const majorityThreshold = Math.ceil(territories.length / 2);
    if (player0Territories.length > majorityThreshold) {
      endGame(0, "Majority Control");
      return;
    }
    if (player1Territories.length > majorityThreshold) {
      endGame(1, "Majority Control");
      return;
    }
  };

  const endGame = async (winner: 0 | 1, victoryType: string) => {
    if (gameState.gameAttemptId) {
      try {
        await completeGameAttempt(
          gameState.gameAttemptId,
          winner === 0 ? 1000 : 0,
          winner === 1 ? 1000 : 0,
          gameState.turnNumber
        );

        toast({
          title: "Game Completed!",
          description: `${gameState.players[winner].name} wins by ${victoryType}! Results saved to repair center.`,
        });
      } catch (error) {
        console.error('Error completing game:', error);
        toast({
          title: "Warning",
          description: "Game completed but results may not have been saved properly.",
          variant: "destructive",
        });
      }
    }

    setGameState(prev => ({
      ...prev,
      phase: 'gameOver',
      gameWinner: winner
    }));
  };

  const nextTurn = () => {
    setGameState(prev => ({
      ...prev,
      phase: 'action',
      currentTurn: prev.currentTurn === 0 ? 1 : 0,
      turnNumber: prev.turnNumber + 1,
      selectedAction: null,
      selectedTerritory: null,
      currentQuestion: null,
      currentQuestions: [],
      questionsAnswered: 0,
      questionsRequired: 1,
      playerAnswer: null,
      correctAnswer: null,
      questionExplanation: null,
      isLoadingExplanation: false,
      timeRemaining: 60,
      actionStartTime: null,
      players: prev.players.map((p, i) => ({ ...p, isActive: i === (prev.currentTurn === 0 ? 1 : 0) })) as [Player, Player] as [Player, Player]
    }));
    setShowAdditionalLearning(false);
  };

  const resetGame = () => {
    setGameState({
      phase: 'setup',
      currentTurn: 0,
      players: [
        { name: '', isActive: false },
        { name: '', isActive: false }
      ],
      territories: [],
      selectedAction: null,
      selectedTerritory: null,
      currentQuestion: null,
      currentQuestions: [],
      questionsAnswered: 0,
      questionsRequired: 1,
      playerAnswer: null,
      correctAnswer: null,
      questionExplanation: null,
      isLoadingExplanation: false,
      timeRemaining: 60,
      gameWinner: null,
      gameAttemptId: null,
      actionStartTime: null,
      usedQuestionIds: new Set(),
      turnNumber: 1
    });
    setPlayerInputs({ player1Name: '', player2Name: '' });
    setShowAdditionalLearning(false);

    // Reinitialize territories
    if (questions.length > 0) {
      initializeTerritories(questions);
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading game questions...</p>
        </div>
      </div>
    );
  }

  if (questions.length < 20) {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <Map className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Insufficient Questions
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You need at least 20 questions to play Domain Domination. You currently have {questions.length} questions.
          </p>
          <Button onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-3 rounded-xl shadow-lg">
              <Map className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                GDPR Domain Domination
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Strategic Territory Conquest
              </p>
            </div>
          </div>
        </div>
        
        {gameState.phase !== 'setup' && (
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-sm">
              Turn {gameState.turnNumber}
            </Badge>
            <Badge variant="outline" className="text-sm">
              {gameState.players[gameState.currentTurn].name}&apos;s Turn
            </Badge>
          </div>
        )}
      </div>

      {/* Game Setup Phase */}
      {gameState.phase === 'setup' && (
        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Game Setup
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              Enter player names to begin the Domain Domination challenge
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="player1">Player 1 Name</Label>
                <Input
                  id="player1"
                  placeholder="Enter player 1 name"
                  value={playerInputs.player1Name}
                  onChange={(e) => setPlayerInputs(prev => ({ ...prev, player1Name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="player2">Player 2 Name</Label>
                <Input
                  id="player2"
                  placeholder="Enter player 2 name"
                  value={playerInputs.player2Name}
                  onChange={(e) => setPlayerInputs(prev => ({ ...prev, player2Name: e.target.value }))}
                />
              </div>
            </div>
            
            <div className="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
              <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-3">Game Rules:</h3>
              <ul className="text-sm text-slate-700 dark:text-slate-300 space-y-2">
                <li>• <strong>Objective:</strong> Control majority of territories, capture both capitals, or achieve total domination</li>
                <li>• <strong>Actions:</strong> CAPTURE neutral territories, INVADE opponent territories, or FORTIFY your own</li>
                <li>• <strong>CAPTURE:</strong> Answer 1 question correctly to claim a neutral territory</li>
                <li>• <strong>INVADE:</strong> Answer 2 questions correctly to steal an opponent&apos;s territory</li>
                <li>• <strong>FORTIFY:</strong> Answer 1 question correctly to make your territory harder to invade (requires 3 correct answers)</li>
                <li>• <strong>Capitals:</strong> Special territories marked with ★ - controlling both wins the game</li>
                <li>• <strong>Time Limit:</strong> 60 seconds per question</li>
              </ul>
            </div>
            
            <Button
              onClick={startGame}
              className="w-full"
              size="lg"
            >
              <Play className="h-4 w-4 mr-2" />
              Start Game
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Action Selection Phase */}
      {gameState.phase === 'action' && (
        <div className="space-y-6">
          {/* Current Player Display */}
          <Card className="border-2 border-purple-500 bg-purple-50 dark:bg-purple-900/20">
            <CardContent className="p-4">
              <div className="flex items-center justify-center gap-3">
                <Crown className="h-6 w-6 text-purple-600" />
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {gameState.players[gameState.currentTurn].name}&apos;s Turn
                  </div>
                  <div className="text-sm text-purple-500">
                    Choose your action
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Action Selection */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                gameState.selectedAction === 'capture' ? 'ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20' : ''
              }`}
              onClick={() => selectAction('capture')}
            >
              <CardContent className="p-6 text-center">
                <Target className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">CAPTURE</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Claim a neutral territory
                </p>
                <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                  1 Question
                </Badge>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                gameState.selectedAction === 'invade' ? 'ring-2 ring-red-500 bg-red-50 dark:bg-red-900/20' : ''
              }`}
              onClick={() => selectAction('invade')}
            >
              <CardContent className="p-6 text-center">
                <Sword className="h-8 w-8 text-red-600 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">INVADE</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Attack opponent territory
                </p>
                <Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                  2-3 Questions
                </Badge>
              </CardContent>
            </Card>

            <Card
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                gameState.selectedAction === 'fortify' ? 'ring-2 ring-green-500 bg-green-50 dark:bg-green-900/20' : ''
              }`}
              onClick={() => selectAction('fortify')}
            >
              <CardContent className="p-6 text-center">
                <Shield className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-2">FORTIFY</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  Strengthen your territory
                </p>
                <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                  1 Question
                </Badge>
              </CardContent>
            </Card>
          </div>

          {/* Territory Board */}
          {gameState.selectedAction && (
            <Card>
              <CardHeader>
                <CardTitle className="text-center">
                  Select Territory for {gameState.selectedAction.toUpperCase()}
                </CardTitle>
                <p className="text-center text-gray-600 dark:text-gray-400">
                  {gameState.selectedAction === 'capture' && "Choose a neutral territory (gray)"}
                  {gameState.selectedAction === 'invade' && "Choose an opponent's territory"}
                  {gameState.selectedAction === 'fortify' && "Choose one of your territories"}
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {gameState.territories.map((territory) => {
                    const isSelectable =
                      (gameState.selectedAction === 'capture' && territory.owner === null) ||
                      (gameState.selectedAction === 'invade' && territory.owner !== null && territory.owner !== gameState.currentTurn) ||
                      (gameState.selectedAction === 'fortify' && territory.owner === gameState.currentTurn);

                    const ownerColor = territory.owner === 0 ? 'border-blue-500 bg-blue-100 dark:bg-blue-900/30' :
                                     territory.owner === 1 ? 'border-red-500 bg-red-100 dark:bg-red-900/30' :
                                     'border-gray-300 bg-gray-100 dark:bg-gray-800';

                    return (
                      <Card
                        key={territory.id}
                        className={`cursor-pointer transition-all duration-200 border-2 ${ownerColor} ${
                          isSelectable ? 'hover:shadow-lg hover:scale-105' : 'opacity-50 cursor-not-allowed'
                        }`}
                        onClick={() => isSelectable && selectTerritory(territory.id)}
                      >
                        <CardContent className="p-4 text-center">
                          <div className="flex items-center justify-center gap-2 mb-2">
                            {territory.isCapital && <Star className="h-4 w-4 text-yellow-500" />}
                            {territory.isFortified && <Shield className="h-4 w-4 text-green-600" />}
                          </div>
                          <h4 className="font-semibold text-sm mb-2">{territory.name}</h4>
                          <div className="text-xs text-gray-600 dark:text-gray-400">
                            {territory.owner === null ? 'Neutral' :
                             `${gameState.players[territory.owner].name}`}
                          </div>
                          {territory.isCapital && (
                            <Badge className="mt-2 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 text-xs">
                              Capital
                            </Badge>
                          )}
                          {territory.isFortified && (
                            <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs">
                              Fortified
                            </Badge>
                          )}
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Question Phase */}
      {gameState.phase === 'question' && (
        <div className="space-y-6">
          {/* Progress and Timer */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-lg font-bold text-gray-900 dark:text-white">
                  Question {gameState.questionsAnswered + 1} of {gameState.questionsRequired}
                </div>
                <div className="text-sm text-gray-500">
                  {gameState.selectedAction?.toUpperCase()} Challenge
                </div>
              </CardContent>
            </Card>

            <Card className={`border-2 ${gameState.timeRemaining <= 10 ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'}`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-center gap-3">
                  <Clock className={`h-6 w-6 ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-blue-600'}`} />
                  <div className="text-center">
                    <div className={`text-2xl font-bold ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-blue-600'}`}>
                      {Math.floor(gameState.timeRemaining / 60)}:{(gameState.timeRemaining % 60).toString().padStart(2, '0')}
                    </div>
                    <div className={`text-sm ${gameState.timeRemaining <= 10 ? 'text-red-500' : 'text-blue-500'}`}>
                      Time Remaining
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Current Question */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">
                {gameState.territories.find(t => t.id === gameState.selectedTerritory)?.name}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>

              <div className="grid grid-cols-1 gap-3">
                {['A', 'B', 'C', 'D'].map((choice) => (
                  <Button
                    key={choice}
                    variant={currentAnswer === choice ? "default" : "outline"}
                    className="text-left justify-start p-4 h-auto"
                    onClick={() => setCurrentAnswer(choice)}
                  >
                    <span className="font-bold mr-3">{choice}.</span>
                    <span>{gameState.currentQuestion?.[`choice${choice}` as keyof Question] as string}</span>
                  </Button>
                ))}
              </div>

              <Button
                onClick={submitAnswer}
                className={`w-full ${gameState.timeRemaining <= 10 ? 'bg-red-600 hover:bg-red-700 animate-pulse' : ''}`}
                disabled={!currentAnswer}
              >
                <Target className="h-4 w-4 mr-2" />
                {gameState.timeRemaining <= 10 ? 'Submit Now!' : 'Submit Answer'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Result Phase */}
      {gameState.phase === 'result' && (
        <div className="space-y-6">
          {/* Result Summary */}
          <Card className={`border-2 ${
            gameState.playerAnswer === gameState.correctAnswer
              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
              : 'border-red-500 bg-red-50 dark:bg-red-900/20'
          }`}>
            <CardContent className="p-6 text-center">
              <div className="flex items-center justify-center gap-3 mb-4">
                {gameState.playerAnswer === gameState.correctAnswer ? (
                  <Trophy className="h-8 w-8 text-green-600" />
                ) : (
                  <Target className="h-8 w-8 text-red-600" />
                )}
                <div>
                  <div className={`text-2xl font-bold ${
                    gameState.playerAnswer === gameState.correctAnswer ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {gameState.playerAnswer === gameState.correctAnswer ? 'Success!' : 'Failed!'}
                  </div>
                  <div className="text-sm text-gray-500">
                    {gameState.selectedAction?.toUpperCase()} attempt
                  </div>
                </div>
              </div>

              {gameState.playerAnswer === gameState.correctAnswer && gameState.questionsAnswered >= gameState.questionsRequired && (
                <div className="text-green-600 font-semibold">
                  Action completed successfully!
                </div>
              )}

              {gameState.playerAnswer !== gameState.correctAnswer && (
                <div className="text-red-600 font-semibold">
                  Action failed. Turn ends.
                </div>
              )}
            </CardContent>
          </Card>

          {/* Question Analysis - Similar to PrivacyStakeGame */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Answer Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Question */}
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>

              {/* Loading State */}
              {gameState.isLoadingExplanation && (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-purple-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">Generating detailed explanation...</p>
                </div>
              )}

              {/* Answer Choices with Explanations */}
              {!gameState.isLoadingExplanation && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center mb-4">
                    Answer Analysis
                  </h3>

                  <div className="grid gap-4">
                    {['A', 'B', 'C', 'D'].map((choice) => {
                      const isCorrect = choice === gameState.correctAnswer;
                      const isPlayerChoice = choice === gameState.playerAnswer;
                      const choiceExplanation = gameState.questionExplanation?.choiceExplanations.find(
                        exp => exp.choice === choice
                      );

                      return (
                        <div
                          key={choice}
                          className={`p-4 rounded-lg border-2 ${
                            isCorrect
                              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                              : isPlayerChoice
                              ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                              : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800'
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                              isCorrect ? 'bg-green-500' : isPlayerChoice ? 'bg-red-500' : 'bg-gray-400'
                            }`}>
                              {choice}
                            </div>
                            <div className="flex-1">
                              <div className="font-medium text-gray-900 dark:text-white mb-2">
                                {gameState.currentQuestion?.[`choice${choice}` as keyof Question] as string}
                              </div>

                              {choiceExplanation && (
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                  <p className="mb-2">{choiceExplanation.explanation}</p>
                                  <p className="italic">{choiceExplanation.reasoning}</p>
                                </div>
                              )}

                              <div className="flex items-center gap-2 mt-2">
                                {isCorrect && (
                                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                    Correct Answer
                                  </Badge>
                                )}
                                {isPlayerChoice && !isCorrect && (
                                  <Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                    Your Choice
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Additional Learning Content */}
              {gameState.questionExplanation && (
                <div className="space-y-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAdditionalLearning(!showAdditionalLearning)}
                    className="w-full"
                  >
                    <BookOpen className="h-4 w-4 mr-2" />
                    {showAdditionalLearning ? 'Hide' : 'Show'} Additional Learning
                    {showAdditionalLearning ? <ChevronUp className="h-4 w-4 ml-2" /> : <ChevronDown className="h-4 w-4 ml-2" />}
                  </Button>

                  {showAdditionalLearning && (
                    <div className="space-y-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                      {/* Key Points */}
                      {gameState.questionExplanation.keyPoints && gameState.questionExplanation.keyPoints.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2 flex items-center gap-2">
                            <Star className="h-4 w-4" />
                            Key Points
                          </h4>
                          <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-300">
                            {gameState.questionExplanation.keyPoints.map((point, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                {point}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Study Tips */}
                      {gameState.questionExplanation.studyTips && gameState.questionExplanation.studyTips.length > 0 && (
                        <div>
                          <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2 flex items-center gap-2">
                            <Lightbulb className="h-4 w-4" />
                            Study Tips
                          </h4>
                          <ul className="space-y-1 text-sm text-blue-800 dark:text-blue-300">
                            {gameState.questionExplanation.studyTips.map((tip, index) => (
                              <li key={index} className="flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                                {tip}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Next Turn Button */}
              <Button onClick={nextTurn} className="w-full" size="lg">
                <Play className="h-4 w-4 mr-2" />
                Next Turn
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Game Over Phase */}
      {gameState.phase === 'gameOver' && (
        <div className="space-y-6">
          {/* Victory Display */}
          <Card className="border-2 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20">
            <CardContent className="p-8 text-center">
              <div className="flex items-center justify-center gap-4 mb-6">
                <Crown className="h-12 w-12 text-yellow-600" />
                <Trophy className="h-16 w-16 text-yellow-600" />
                <Crown className="h-12 w-12 text-yellow-600" />
              </div>

              <h2 className="text-3xl font-bold text-yellow-800 dark:text-yellow-300 mb-2">
                Victory!
              </h2>

              <div className="text-xl font-semibold text-yellow-700 dark:text-yellow-400 mb-4">
                {gameState.players[gameState.gameWinner!].name} Wins!
              </div>

              <div className="text-yellow-600 dark:text-yellow-500">
                Congratulations on your strategic mastery of GDPR domains!
              </div>
            </CardContent>
          </Card>

          {/* Final Territory Control */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Final Territory Control</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                {gameState.territories.map((territory) => {
                  const ownerColor = territory.owner === 0 ? 'border-blue-500 bg-blue-100 dark:bg-blue-900/30' :
                                   territory.owner === 1 ? 'border-red-500 bg-red-100 dark:bg-red-900/30' :
                                   'border-gray-300 bg-gray-100 dark:bg-gray-800';

                  return (
                    <Card
                      key={territory.id}
                      className={`border-2 ${ownerColor}`}
                    >
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center gap-2 mb-2">
                          {territory.isCapital && <Star className="h-4 w-4 text-yellow-500" />}
                          {territory.isFortified && <Shield className="h-4 w-4 text-green-600" />}
                        </div>
                        <h4 className="font-semibold text-sm mb-2">{territory.name}</h4>
                        <div className="text-xs text-gray-600 dark:text-gray-400">
                          {territory.owner === null ? 'Neutral' :
                           `${gameState.players[territory.owner].name}`}
                        </div>
                        {territory.isCapital && (
                          <Badge className="mt-2 bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 text-xs">
                            Capital
                          </Badge>
                        )}
                        {territory.isFortified && (
                          <Badge className="mt-2 bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 text-xs">
                            Fortified
                          </Badge>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Player Statistics */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                {gameState.players.map((player, index) => {
                  const playerTerritories = gameState.territories.filter(t => t.owner === index);
                  const playerCapitals = playerTerritories.filter(t => t.isCapital);
                  const playerFortified = playerTerritories.filter(t => t.isFortified);

                  return (
                    <Card key={index} className={`${index === gameState.gameWinner ? 'ring-2 ring-yellow-500' : ''}`}>
                      <CardContent className="p-4">
                        <div className="flex items-center gap-2 mb-3">
                          {index === gameState.gameWinner && <Crown className="h-5 w-5 text-yellow-500" />}
                          <h3 className="font-bold text-lg">{player.name}</h3>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Territories:</span>
                            <span className="font-semibold">{playerTerritories.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Capitals:</span>
                            <span className="font-semibold">{playerCapitals.length}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Fortified:</span>
                            <span className="font-semibold">{playerFortified.length}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-4 justify-center">
                <Button onClick={resetGame} variant="outline" size="lg">
                  <Play className="h-4 w-4 mr-2" />
                  Play Again
                </Button>
                <Button onClick={onBack} size="lg">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Games
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

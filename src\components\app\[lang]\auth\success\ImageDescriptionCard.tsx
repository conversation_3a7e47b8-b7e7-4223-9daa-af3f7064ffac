"use client";

import React, { useState, useRef, useCallback } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, FileImage, X, Sparkles } from "lucide-react";
import { Dictionary } from "@/dictionaries";
import { useToast } from "@/components/ui/use-toast";
// import { Input } from "@/components/ui/input"; // Reusing Input style for file input trigger - This is now unused
import Image from "next/image";
/* Unused Dropdown imports
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
*/

interface ImageDescriptionCardProps {
  dict: Dictionary;
}

export function ImageDescriptionCard({ dict }: ImageDescriptionCardProps) {
  const { toast } = useToast();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [description, setDescription] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith("image/")) {
        toast({
          title: dict.ai.imageDescription?.errorTitle || "Error",
          description: dict.ai.imageDescription?.errorInvalidFileType || "Please select an image file.",
          variant: "destructive",
        });
        return;
      }
      // Limit file size client-side (e.g., 10MB)
      const maxSize = 10 * 1024 * 1024; // 10 MB
      if (file.size > maxSize) {
         toast({
            title: dict.ai.imageDescription?.errorTitle || "Error",
            description: `${dict.ai.imageDescription?.errorFileSize || 'File size exceeds the limit of 10MB.'}`,
            variant: "destructive",
         });
         return;
      }

      setSelectedFile(file);
      setDescription(null); // Clear previous description
      setError(null);
      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    } else {
       // Handle case where user cancels file selection
       // No explicit action needed here unless you want to clear state
    }
  };

  const handleDescribeImage = useCallback(async () => {
    if (!selectedFile) return;

    setIsLoading(true);
    setError(null);
    setDescription(null);

    const formData = new FormData();
    formData.append("image", selectedFile);

    try {
      const response = await fetch("/api/ImageDescription", {
        method: "POST",
        body: formData,
      });

      const rateLimitLimit = response.headers.get('X-RateLimit-Limit');
      const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
      console.log("Rate Limit Headers:", { rateLimitLimit, rateLimitRemaining });

      if (!response.ok) {
         const errorText = await response.text();
         let displayError = errorText || (dict.ai.imageDescription?.errorGeneric || "Failed to describe image.");
         if (response.status === 429) {
            displayError = dict.ai.imageDescription?.errorRateLimit || "Too many requests. Try again later.";
         } else if (response.status === 413) {
            displayError = dict.ai.imageDescription?.errorFileSize || "Image file is too large.";
         }

         throw new Error(displayError);
      }

      const result = await response.json();
      setDescription(result.description);

    } catch (err: unknown) {
      console.error("Image Description Error:", err);
      const message = err instanceof Error ? err.message : String(err);
      setError(message);
      toast({
        title: dict.ai.imageDescription?.errorTitle || "Error",
        description: message,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedFile, dict, toast]);

  const handleRemoveImage = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setDescription(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = ""; // Reset file input
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileImage className="mr-2 h-5 w-5 text-purple-500" />
          {dict.ai.imageDescription?.title || "Image Describer"}
        </CardTitle>
        <CardDescription>
          {dict.ai.imageDescription?.description ||
            "Upload an image and let AI describe it for you."}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Hidden actual file input */}
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept="image/*" // Accept only image files
          className="hidden"
        />

        {/* Image Preview and Controls */}
        {previewUrl && selectedFile && (
          <div className="relative group border rounded-md overflow-hidden aspect-video w-full max-w-sm mx-auto">
            <Image
              src={previewUrl}
              alt="Selected preview"
              layout="fill"
              objectFit="contain"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleRemoveImage}
              title="Remove image"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        )}

        {/* Placeholder or Upload Trigger */}
        {!previewUrl && (
           <div 
             className="border-2 border-dashed border-muted-foreground/50 rounded-md p-6 text-center cursor-pointer hover:border-primary transition-colors flex flex-col items-center justify-center aspect-video w-full max-w-sm mx-auto"
             onClick={triggerFileInput}
             role="button"
             tabIndex={0}
             onKeyDown={(e) => { if(e.key === 'Enter' || e.key === ' ') triggerFileInput() }}
             aria-label={dict.ai.imageDescription?.uploadArea || "Select an image"}
           >
             <FileImage className="h-10 w-10 text-muted-foreground mb-2" />
             <p className="text-sm text-muted-foreground">
               {dict.ai.imageDescription?.uploadPrompt || "Click or tap here to select an image"} (Max 10MB)
             </p>
           </div>
        )}
        
        {/* Description Area */}
        {(description || isLoading || error) && (
          <div className="min-h-[50px] pt-4">
            {isLoading && (
              <div className="flex items-center space-x-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{dict.ai.imageDescription?.loading || "Describing image"}...</span>
              </div>
            )}
            {description && !isLoading && (
              <div className="text-sm text-foreground whitespace-pre-wrap border-l-2 border-purple-500 pl-3">
                {description}
              </div>
            )}
            {error && !isLoading && (
              <p className="text-sm text-destructive">
                {dict.ai.imageDescription?.errorPrefix || "Error"}: {error}
              </p>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleDescribeImage}
          disabled={isLoading || !selectedFile}
        >
          {isLoading ? (
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          ) : (
            <Sparkles className="mr-2 h-4 w-4" /> // Using Sparkles like NameMeaning
          )}
          {isLoading
            ? dict.ai.imageDescription?.buttonLoading || "Describing..."
            : dict.ai.imageDescription?.buttonDefault || "Describe Image"}
        </Button>
      </CardFooter>
    </Card>
  );
} 
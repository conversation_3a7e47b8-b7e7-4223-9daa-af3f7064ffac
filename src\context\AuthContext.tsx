"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { User } from "firebase/auth";
import {
  loginWithEmailAndPassword,
  registerWithEmailAndPassword,
  signInWithGoogle,
  logoutUser,
  sendUserEmailVerification,
  resetPassword,
  auth,
  onAuthStateChanged
} from "@/Firebase/Authentication/authConfig";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { upsertUserProfile } from "@/Firebase/firestore/services/UserService";

// Define the shape of the context
interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<User>;
  register: (email: string, password: string, name: string) => Promise<User>;
  loginWithGoogle: () => Promise<User>;
  logout: () => Promise<void>;
  sendEmailVerification: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  clearError: () => void;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component that wraps your app and makes auth object available to any child component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Clear any previous errors
  const clearError = () => setError(null);

  // Listen for auth state changes when the component mounts
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (authUser) => {
      setUser(authUser);
      setLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  // Log in with email and password
  const login = async (email: string, password: string): Promise<User> => {
    try {
      clearError();
      setLoading(true);
      const user = await loginWithEmailAndPassword(email, password);
      return user;
    } catch (error) {
      const errorMessage = handleAuthError(error);
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Register with email and password
  const register = async (email: string, password: string, name: string): Promise<User> => {
    try {
      clearError();
      setLoading(true);
      const user = await registerWithEmailAndPassword(email, password);
      
      // Create user profile in Firestore
      await upsertUserProfile(user.uid, {
        email,
        displayName: name,
      });
      
      return user;
    } catch (error) {
      const errorMessage = handleAuthError(error);
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign in with Google
  const loginWithGoogle = async (): Promise<User> => {
    try {
      clearError();
      setLoading(true);
      const user = await signInWithGoogle();
      
      // Create or update user profile in Firestore
      await upsertUserProfile(user.uid, {
        email: user.email || "",
        displayName: user.displayName || "",
        photoURL: user.photoURL,
      });
      
      return user;
    } catch (error) {
      const errorMessage = handleAuthError(error);
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Sign out
  const logout = async (): Promise<void> => {
    try {
      clearError();
      setLoading(true);
      await logoutUser();
    } catch (error) {
      const errorMessage = handleAuthError(error);
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Send email verification
  const sendEmailVerification = async (): Promise<void> => {
    try {
      clearError();
      await sendUserEmailVerification();
    } catch (error) {
      const errorMessage = handleAuthError(error);
      setError(errorMessage);
      throw error;
    }
  };

  // Create the value object that will be provided by the context
  const value = {
    user,
    loading,
    error,
    login,
    register,
    loginWithGoogle,
    logout,
    sendEmailVerification,
    resetPassword,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  
  return context;
} 
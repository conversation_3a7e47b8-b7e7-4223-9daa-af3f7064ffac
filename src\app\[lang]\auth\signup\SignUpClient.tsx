"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { z } from "zod";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { useAuth } from "@/context";

export default function SignUpClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const { toast } = useToast();
  const { register, loginWithGoogle, sendEmailVerification, loading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Form schema with validation
  const formSchema = z.object({
    name: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
    password: z.string().min(8, {
      message: dict.auth.common.passwordRequirements,
    }),
    confirmPassword: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
  }).refine((data) => data.password === data.confirmPassword, {
    message: dict.auth.common.passwordMismatch,
    path: ["confirmPassword"],
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // Register the user with our context
      await register(data.email, data.password, data.name);

      // Send email verification
      await sendEmailVerification();

      toast({
        title: dict.auth.signup.success,
        variant: "default",
      });

      // Redirect to verification page
      router.push("verify");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signup.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      await loginWithGoogle();

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });

      router.push("success");
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthCard
      title={dict.auth.signup.title}
      description={dict.auth.signup.subtitle}
      footer={
        <div className="text-center mt-4">
          <p className="text-sm text-muted-foreground">
            {dict.auth.common.alreadyHaveAccount}{" "}
            <Link
              href="signin"
              className="text-primary underline underline-offset-4 hover:text-primary/90"
            >
              {dict.auth.signin.title}
            </Link>
          </p>
        </div>
      }
    >
      <AuthForm
        schema={formSchema}
        onSubmit={onSubmit}
        submitText={dict.auth.signup.button}
        isLoading={isLoading || authLoading}
        googleSignIn={handleGoogleSignIn}
        googleText={dict.auth.common.continueWithGoogle}
      >
        <FormField
          name="name"
          label={dict.auth.common.name}
          placeholder="John Doe"
          required
          autoComplete="name"
        />
        <FormField
          name="email"
          label={dict.auth.common.email}
          placeholder="<EMAIL>"
          type="email"
          required
          autoComplete="email"
        />
        <FormField
          name="password"
          label={dict.auth.common.password}
          placeholder="••••••••"
          type="password"
          required
          autoComplete="new-password"
          description={dict.auth.common.passwordRequirements}
        />
        <FormField
          name="confirmPassword"
          label={dict.auth.common.confirmPassword}
          placeholder="••••••••"
          type="password"
          required
          autoComplete="new-password"
        />
        <p className="text-xs text-muted-foreground mt-2">
          {dict.auth.signup.termsAndConditions}
        </p>
      </AuthForm>
    </AuthCard>
  );
}

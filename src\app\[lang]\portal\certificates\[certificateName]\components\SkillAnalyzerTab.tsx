"use client";

import React from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { TrendingUp, Plus, Brain, Target, AlertTriangle } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SkillAnalyzerTabProps {
  certificate: Certificate;
}

export default function SkillAnalyzerTab({}: SkillAnalyzerTabProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-purple-50 dark:from-gray-900 dark:to-purple-900/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Skill Analyzer
            </h2>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
              Identify your strengths and weaknesses to focus your study efforts
            </p>
          </div>
          <Button className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-sm" size="sm">
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">Start Analysis</span>
            <span className="sm:hidden">Analyze</span>
          </Button>
        </div>

        {/* Analysis Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Strong Areas</p>
                <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">0</p>
              </div>
              <div className="bg-green-100 dark:bg-green-900/20 p-2 sm:p-3 rounded-xl">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Weak Areas</p>
                <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">0</p>
              </div>
              <div className="bg-red-100 dark:bg-red-900/20 p-2 sm:p-3 rounded-xl">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-red-600" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg sm:col-span-2 lg:col-span-1">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Overall Score</p>
                <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">0%</p>
              </div>
              <div className="bg-purple-100 dark:bg-purple-900/20 p-2 sm:p-3 rounded-xl">
                <Brain className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Empty State */}
        <div className="text-center py-20">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-12 shadow-xl max-w-md mx-auto">
            <div className="bg-gradient-to-r from-purple-600 to-indigo-600 p-4 rounded-full w-16 h-16 mx-auto mb-6">
              <TrendingUp className="h-8 w-8 text-white mx-auto" />
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">
              No analysis data yet
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-6">
              Complete some practice questions to get personalized insights into your strengths and areas for improvement
            </p>
            <Button className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700">
              <Plus className="h-4 w-4 mr-2" />
              Start Your First Analysis
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  Calendar,
  Target,
  Building,
  ArrowLeft,
  HelpCircle,
  TrendingUp,
  FileText,
  Loader2,
  Wrench,
  Gamepad2
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  getUserCertificates,
  Certificate
} from "@/Firebase/firestore/services/CertificatesService";
import { matchCertificateBySlug } from "@/lib/certificateUtils";
import QuestionBankTab from "./components/QuestionBankTab";
import SkillAnalyzerTab from "./components/SkillAnalyzerTab";
import TakeExamTab from "./components/TakeExamTab";
import RepairCenterTab from "./components/RepairCenterTab";
import OneVOneGamesTab from "./components/OneVOneGamesTab";

interface CertificateDetailClientProps {
  certificateName: string;
  dict?: Record<string, unknown>;
}

export default function CertificateDetailClient({
  certificateName
}: CertificateDetailClientProps) {
  const [certificate, setCertificate] = useState<Certificate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("questions");
  
  const { user } = useAuth();
  const { toast } = useToast();
  const pathname = usePathname();
  const lang = pathname.split('/')[1];

  const loadCertificate = useCallback(async () => {
    if (!user?.uid) return;
    
    try {
      setIsLoading(true);
      const certificates = await getUserCertificates(user.uid);

      // Find certificate by name or slug
      const foundCertificate = matchCertificateBySlug(certificates, certificateName);
      
      if (foundCertificate) {
        setCertificate(foundCertificate);
      } else {
        toast({
          title: "Certificate not found",
          description: "The requested certificate could not be found.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error loading certificate:', error);
      toast({
        title: "Error",
        description: "Failed to load certificate details.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificateName, toast]);

  useEffect(() => {
    if (user?.uid) {
      loadCertificate();
    }
  }, [user?.uid, certificateName, loadCertificate]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'studying': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'scheduled': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'expired': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default: return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      default: return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    }
  };

  const tabs = [
    {
      id: "questions",
      name: "Question Bank",
      icon: HelpCircle,
      component: QuestionBankTab
    },
    {
      id: "exam",
      name: "Take Exam",
      icon: FileText,
      component: TakeExamTab
    },
    {
      id: "repair",
      name: "Repair Center",
      icon: Wrench,
      component: RepairCenterTab
    },
    {
      id: "analyzer",
      name: "Skill Analyzer",
      icon: TrendingUp,
      component: SkillAnalyzerTab
    },
    {
      id: "games",
      name: "1V1 Games",
      icon: Gamepad2,
      component: OneVOneGamesTab
    }
  ];

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading certificate details...</p>
        </div>
      </div>
    );
  }

  if (!certificate) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Award className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Certificate Not Found</h2>
          <p className="text-gray-500 dark:text-gray-400 mb-6">The requested certificate could not be found.</p>
          <Link href={`/${lang}/portal/certificates`}>
            <Button>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Certificates
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const ActiveTabComponent = tabs.find(tab => tab.id === activeTab)?.component || QuestionBankTab;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      {/* Hero Section */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
          {/* Back Button */}
          <div className="mb-4 sm:mb-6">
            <Link href={`/${lang}/portal/certificates`}>
              <Button variant="ghost" className="text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white text-sm sm:text-base">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Certificates
              </Button>
            </Link>
          </div>

          {/* Certificate Header */}
          <div className="flex flex-col gap-4 sm:gap-6">
            <div className="flex-1">
              <div className="flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4 mb-4">
                <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 sm:p-4 rounded-2xl shadow-lg self-start">
                  <Award className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2 break-words">
                    {certificate.name}
                  </h1>
                  {certificate.provider && (
                    <div className="flex items-center text-base sm:text-lg text-blue-600 dark:text-blue-400 mb-2 sm:mb-3">
                      <Building className="h-4 w-4 sm:h-5 sm:w-5 mr-2 flex-shrink-0" />
                      <span className="truncate">{certificate.provider}</span>
                    </div>
                  )}
                  <p className="text-gray-600 dark:text-gray-400 text-sm sm:text-base lg:text-lg leading-relaxed">
                    {certificate.description}
                  </p>
                </div>
              </div>
            </div>

            {/* Certificate Stats */}
            <div className="w-full">
              <div className="bg-gradient-to-br from-white to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-2xl p-4 sm:p-6 shadow-lg">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
                  <div className="flex items-center justify-between sm:flex-col sm:items-start sm:space-y-1">
                    <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Status</span>
                    <Badge className={`capitalize text-xs ${getStatusColor(certificate.status)}`}>
                      {certificate.status.replace('_', ' ')}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between sm:flex-col sm:items-start sm:space-y-1">
                    <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Priority</span>
                    <Badge className={`capitalize text-xs ${getPriorityColor(certificate.priority)}`}>
                      {certificate.priority} Priority
                    </Badge>
                  </div>

                  {certificate.targetDate && (
                    <div className="flex items-center justify-between sm:flex-col sm:items-start sm:space-y-1">
                      <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Target Date</span>
                      <div className="flex items-center text-xs sm:text-sm text-gray-900 dark:text-white">
                        <Target className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                        <span className="truncate">{new Date(certificate.targetDate).toLocaleDateString()}</span>
                      </div>
                    </div>
                  )}

                  <div className="flex items-center justify-between sm:flex-col sm:items-start sm:space-y-1">
                    <span className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Created</span>
                    <div className="flex items-center text-xs sm:text-sm text-gray-900 dark:text-white">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                      <span className="truncate">{certificate.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex overflow-x-auto scrollbar-hide">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-1 sm:space-x-2 py-3 sm:py-4 px-3 sm:px-4 border-b-2 font-medium text-xs sm:text-sm transition-colors whitespace-nowrap flex-shrink-0 ${
                    activeTab === tab.id
                      ? 'border-blue-600 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                  <span className="hidden sm:inline">{tab.name}</span>
                  <span className="sm:hidden">{tab.name.split(' ')[0]}</span>
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <ActiveTabComponent certificate={certificate} />
    </div>
  );
}

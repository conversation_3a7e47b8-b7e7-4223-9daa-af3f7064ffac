"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  HelpCircle,
  Plus,
  Target,
  BarChart3,
  Upload,
  Download,
  Loader2,
  FileSpreadsheet,
  Tag,
  Sparkles
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { ExcelImportModal } from "@/components/ui/Questions/ExcelImportModal";
import {
  getCertificateQuestions,
  importQuestionsFromExcel,
  exportQuestionsToExcel,
  updateQuestionTags,
  Question,
  ExcelQuestionRow
} from "@/Firebase/firestore/services/QuestionsService";
import * as XLSX from 'xlsx';

interface QuestionBankTabProps {
  certificate: Certificate;
}

// GDPR Topic Color Mapping
const TOPIC_COLORS: Record<string, { bg: string; border: string; text: string; hover: string }> = {
  "Data Protection Principles": { bg: "bg-blue-100 dark:bg-blue-900/20", border: "border-blue-300 dark:border-blue-600", text: "text-blue-800 dark:text-blue-300", hover: "hover:bg-blue-200 dark:hover:bg-blue-800/30" },
  "Lawful Basis for Processing": { bg: "bg-green-100 dark:bg-green-900/20", border: "border-green-300 dark:border-green-600", text: "text-green-800 dark:text-green-300", hover: "hover:bg-green-200 dark:hover:bg-green-800/30" },
  "Consent Management": { bg: "bg-purple-100 dark:bg-purple-900/20", border: "border-purple-300 dark:border-purple-600", text: "text-purple-800 dark:text-purple-300", hover: "hover:bg-purple-200 dark:hover:bg-purple-800/30" },
  "Data Subject Rights": { bg: "bg-red-100 dark:bg-red-900/20", border: "border-red-300 dark:border-red-600", text: "text-red-800 dark:text-red-300", hover: "hover:bg-red-200 dark:hover:bg-red-800/30" },
  "Data Protection Impact Assessment (DPIA)": { bg: "bg-yellow-100 dark:bg-yellow-900/20", border: "border-yellow-300 dark:border-yellow-600", text: "text-yellow-800 dark:text-yellow-300", hover: "hover:bg-yellow-200 dark:hover:bg-yellow-800/30" },
  "Data Protection Officer (DPO)": { bg: "bg-indigo-100 dark:bg-indigo-900/20", border: "border-indigo-300 dark:border-indigo-600", text: "text-indigo-800 dark:text-indigo-300", hover: "hover:bg-indigo-200 dark:hover:bg-indigo-800/30" },
  "International Data Transfers": { bg: "bg-pink-100 dark:bg-pink-900/20", border: "border-pink-300 dark:border-pink-600", text: "text-pink-800 dark:text-pink-300", hover: "hover:bg-pink-200 dark:hover:bg-pink-800/30" },
  "Data Breach Notification": { bg: "bg-orange-100 dark:bg-orange-900/20", border: "border-orange-300 dark:border-orange-600", text: "text-orange-800 dark:text-orange-300", hover: "hover:bg-orange-200 dark:hover:bg-orange-800/30" },
  "Privacy by Design and Default": { bg: "bg-teal-100 dark:bg-teal-900/20", border: "border-teal-300 dark:border-teal-600", text: "text-teal-800 dark:text-teal-300", hover: "hover:bg-teal-200 dark:hover:bg-teal-800/30" },
  "Record Keeping and Documentation": { bg: "bg-cyan-100 dark:bg-cyan-900/20", border: "border-cyan-300 dark:border-cyan-600", text: "text-cyan-800 dark:text-cyan-300", hover: "hover:bg-cyan-200 dark:hover:bg-cyan-800/30" },
  "Supervisory Authority Powers": { bg: "bg-violet-100 dark:bg-violet-900/20", border: "border-violet-300 dark:border-violet-600", text: "text-violet-800 dark:text-violet-300", hover: "hover:bg-violet-200 dark:hover:bg-violet-800/30" },
  "Administrative Fines and Penalties": { bg: "bg-rose-100 dark:bg-rose-900/20", border: "border-rose-300 dark:border-rose-600", text: "text-rose-800 dark:text-rose-300", hover: "hover:bg-rose-200 dark:hover:bg-rose-800/30" },
  "Special Categories of Personal Data": { bg: "bg-emerald-100 dark:bg-emerald-900/20", border: "border-emerald-300 dark:border-emerald-600", text: "text-emerald-800 dark:text-emerald-300", hover: "hover:bg-emerald-200 dark:hover:bg-emerald-800/30" },
  "Children's Data Protection": { bg: "bg-lime-100 dark:bg-lime-900/20", border: "border-lime-300 dark:border-lime-600", text: "text-lime-800 dark:text-lime-300", hover: "hover:bg-lime-200 dark:hover:bg-lime-800/30" },
  "Marketing and Profiling": { bg: "bg-amber-100 dark:bg-amber-900/20", border: "border-amber-300 dark:border-amber-600", text: "text-amber-800 dark:text-amber-300", hover: "hover:bg-amber-200 dark:hover:bg-amber-800/30" },
  "Cookies and Online Tracking": { bg: "bg-slate-100 dark:bg-slate-900/20", border: "border-slate-300 dark:border-slate-600", text: "text-slate-800 dark:text-slate-300", hover: "hover:bg-slate-200 dark:hover:bg-slate-800/30" },
  "Data Retention and Erasure": { bg: "bg-stone-100 dark:bg-stone-900/20", border: "border-stone-300 dark:border-stone-600", text: "text-stone-800 dark:text-stone-300", hover: "hover:bg-stone-200 dark:hover:bg-stone-800/30" },
  "Data Processing Agreements": { bg: "bg-zinc-100 dark:bg-zinc-900/20", border: "border-zinc-300 dark:border-zinc-600", text: "text-zinc-800 dark:text-zinc-300", hover: "hover:bg-zinc-200 dark:hover:bg-zinc-800/30" },
  "Joint Controllers and Processors": { bg: "bg-neutral-100 dark:bg-neutral-900/20", border: "border-neutral-300 dark:border-neutral-600", text: "text-neutral-800 dark:text-neutral-300", hover: "hover:bg-neutral-200 dark:hover:bg-neutral-800/30" },
  "Certification and Codes of Conduct": { bg: "bg-sky-100 dark:bg-sky-900/20", border: "border-sky-300 dark:border-sky-600", text: "text-sky-800 dark:text-sky-300", hover: "hover:bg-sky-200 dark:hover:bg-sky-800/30" }
};

const getTopicColor = (topic?: string) => {
  if (!topic) return { bg: "bg-gray-100 dark:bg-gray-700", border: "border-gray-300 dark:border-gray-600", text: "text-gray-800 dark:text-gray-300", hover: "hover:bg-gray-200 dark:hover:bg-gray-600" };
  return TOPIC_COLORS[topic] || { bg: "bg-gray-100 dark:bg-gray-700", border: "border-gray-300 dark:border-gray-600", text: "text-gray-800 dark:text-gray-300", hover: "hover:bg-gray-200 dark:hover:bg-gray-600" };
};

export default function QuestionBankTab({ certificate }: QuestionBankTabProps) {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isTagging, setIsTagging] = useState(false);
  const [taggingProgress, setTaggingProgress] = useState({ current: 0, total: 0 });
  const [expandedQuestions, setExpandedQuestions] = useState<Set<string>>(new Set());
  const [selectedTopicFilter, setSelectedTopicFilter] = useState<string | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();

  const loadQuestions = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const certificateQuestions = await getCertificateQuestions(user.uid, certificate.id);
      setQuestions(certificateQuestions);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast({
        title: "Error",
        description: "Failed to load questions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    if (user?.uid && certificate.id) {
      loadQuestions();
    }
  }, [user?.uid, certificate.id, loadQuestions]);

  const handleImportQuestions = async (data: ExcelQuestionRow[], importMode: 'replace' | 'append') => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsImporting(true);
      const result = await importQuestionsFromExcel(user.uid, certificate.id, data, importMode);

      toast({
        title: "Import Complete",
        description: `Successfully imported ${result.success} questions. ${result.failed > 0 ? `${result.failed} failed.` : ''}`,
        variant: result.failed > 0 ? "destructive" : "default",
      });

      if (result.errors.length > 0) {
        console.warn('Import errors:', result.errors);
      }

      // Reload questions
      await loadQuestions();
    } catch (error) {
      console.error('Error importing questions:', error);
      toast({
        title: "Import Failed",
        description: "Failed to import questions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsImporting(false);
    }
  };

  const handleExportQuestions = async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsExporting(true);
      const exportData = await exportQuestionsToExcel(user.uid, certificate.id);

      if (exportData.length === 0) {
        toast({
          title: "No Questions",
          description: "There are no questions to export.",
          variant: "destructive",
        });
        return;
      }

      // Create Excel file
      const ws = XLSX.utils.json_to_sheet(exportData.map(q => ({
        'Question': q.question,
        'Correct Answer': q.correctAnswer,
        'Choice A': q.choiceA,
        'Choice B': q.choiceB,
        'Choice C': q.choiceC,
        'Choice D': q.choiceD,
      })));

      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Questions");

      const fileName = `${certificate.name.replace(/[^a-zA-Z0-9]/g, '_')}_questions.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast({
        title: "Export Complete",
        description: `Successfully exported ${exportData.length} questions.`,
        variant: "default",
      });
    } catch (error) {
      console.error('Error exporting questions:', error);
      toast({
        title: "Export Failed",
        description: "Failed to export questions. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const toggleQuestionExpansion = (questionId: string) => {
    setExpandedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(questionId)) {
        newSet.delete(questionId);
      } else {
        newSet.add(questionId);
      }
      return newSet;
    });
  };

  const handleTagAllQuestions = async () => {
    if (!user?.uid || !certificate.id || questions.length === 0) return;

    // Only tag questions for CIPP/E certificate
    if (!certificate.name.toLowerCase().includes('cipp') && !certificate.name.toLowerCase().includes('gdpr')) {
      toast({
        title: "Feature Not Available",
        description: "AI tagging is currently only available for CIPP/E and GDPR certifications.",
        variant: "destructive",
      });
      return;
    }

    // Filter out questions that are already tagged
    const untaggedQuestions = questions.filter(q => !q.category);

    if (untaggedQuestions.length === 0) {
      toast({
        title: "All Questions Tagged",
        description: "All questions already have AI tags. No action needed.",
        variant: "default",
      });
      return;
    }

    try {
      setIsTagging(true);
      setTaggingProgress({ current: 0, total: untaggedQuestions.length });

      const BATCH_SIZE = 50;
      const batches = [];

      // Split questions into batches of 50
      for (let i = 0; i < untaggedQuestions.length; i += BATCH_SIZE) {
        batches.push(untaggedQuestions.slice(i, i + BATCH_SIZE));
      }

      let totalSuccessful = 0;
      let totalFailed = 0;
      const allErrors: string[] = [];

      // Process each batch
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];

        // Update progress
        setTaggingProgress({
          current: batchIndex * BATCH_SIZE,
          total: untaggedQuestions.length
        });

        // Prepare questions data for AI
        const questionsForAI = batch.map(q => ({
          id: q.id,
          question: q.question,
          choiceA: q.choiceA,
          choiceB: q.choiceB,
          choiceC: q.choiceC,
          choiceD: q.choiceD,
          correctAnswer: q.correctAnswer
        }));

        try {
          // Call the bulk tagging API for this batch
          const response = await fetch('/api/BulkQuestionTagging', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              questions: questionsForAI,
              certificateName: certificate.name
            }),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const result = await response.json();

          if (result.success && result.data) {
            // Update questions with tags
            const questionTags = result.data.map((tag: {questionId: string, subtopics?: string[], topic: string}) => ({
              questionId: tag.questionId,
              tags: tag.subtopics || [tag.topic],
              topic: tag.topic
            }));

            const updateResult = await updateQuestionTags(user.uid, certificate.id, questionTags);

            totalSuccessful += updateResult.success;
            totalFailed += updateResult.failed;
            allErrors.push(...updateResult.errors);

            // Show progress toast
            toast({
              title: `Batch ${batchIndex + 1}/${batches.length} Complete`,
              description: `Tagged ${updateResult.success} questions in this batch. ${batch.length - updateResult.success > 0 ? `${batch.length - updateResult.success} failed.` : ''}`,
              variant: updateResult.failed > 0 ? "destructive" : "default",
            });

          } else {
            throw new Error('Invalid response from AI service');
          }
        } catch (batchError) {
          console.error(`Error processing batch ${batchIndex + 1}:`, batchError);
          totalFailed += batch.length;

          toast({
            title: `Batch ${batchIndex + 1} Failed`,
            description: `Failed to process batch of ${batch.length} questions. Continuing with next batch...`,
            variant: "destructive",
          });
        }

        // Add a small delay between batches to prevent overwhelming the API
        if (batchIndex < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Final summary
      setTaggingProgress({ current: untaggedQuestions.length, total: untaggedQuestions.length });

      toast({
        title: "AI Tagging Complete",
        description: `Successfully tagged ${totalSuccessful} questions with GDPR topics. ${totalFailed > 0 ? `${totalFailed} failed.` : ''} Processed in ${batches.length} batches.`,
        variant: totalFailed > 0 ? "destructive" : "default",
      });

      if (allErrors.length > 0) {
        console.warn('Tagging errors:', allErrors);
      }

      // Reload questions to show updated tags
      await loadQuestions();

    } catch (error) {
      console.error('Error tagging questions:', error);
      toast({
        title: "AI Tagging Failed",
        description: "Failed to tag questions with AI. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsTagging(false);
      setTaggingProgress({ current: 0, total: 0 });
    }
  };
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-green-50 dark:from-gray-900 dark:to-green-900/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8 lg:py-12">
        {/* Header */}
        <div className="flex flex-col gap-4 mb-6 sm:mb-8">
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Question Bank
            </h2>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
              Practice questions to test your knowledge and prepare for the exam
            </p>
          </div>
          <div className="flex flex-col sm:flex-row flex-wrap gap-2 sm:gap-3">
            <Button
              onClick={() => setIsImportModalOpen(true)}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-sm"
              size="sm"
            >
              <Upload className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Import Excel</span>
              <span className="sm:hidden">Import</span>
            </Button>
            <Button
              onClick={handleExportQuestions}
              disabled={questions.length === 0 || isExporting}
              variant="outline"
              className="border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-300 dark:hover:bg-green-900/20 text-sm"
              size="sm"
            >
              {isExporting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Download className="h-4 w-4 mr-2" />
              )}
              <span className="hidden sm:inline">Export Excel</span>
              <span className="sm:hidden">Export</span>
            </Button>
            {(certificate.name.toLowerCase().includes('cipp') || certificate.name.toLowerCase().includes('gdpr')) && (
              <Button
                onClick={handleTagAllQuestions}
                disabled={questions.length === 0 || isTagging}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-sm"
                size="sm"
              >
                {isTagging ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    {taggingProgress.total > 0 ? (
                      <>
                        <span className="hidden sm:inline">Tagging {taggingProgress.current}/{taggingProgress.total}</span>
                        <span className="sm:hidden">Tagging...</span>
                      </>
                    ) : (
                      <span>AI Tagging...</span>
                    )}
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">AI Tag Questions (Batches of 50)</span>
                    <span className="sm:hidden">AI Tag</span>
                  </>
                )}
              </Button>
            )}
            <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-sm" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add Question</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-green-600 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">Loading questions...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8">
              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Total Questions</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{questions.length}</p>
                  </div>
                  <div className="bg-blue-100 dark:bg-blue-900/20 p-2 sm:p-3 rounded-xl">
                    <HelpCircle className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">0</p>
                  </div>
                  <div className="bg-green-100 dark:bg-green-900/20 p-2 sm:p-3 rounded-xl">
                    <Target className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                  </div>
                </div>
              </div>

              <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg sm:col-span-2 lg:col-span-1">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs sm:text-sm font-medium text-gray-600 dark:text-gray-400">Accuracy</p>
                    <p className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">0%</p>
                  </div>
                  <div className="bg-purple-100 dark:bg-purple-900/20 p-2 sm:p-3 rounded-xl">
                    <BarChart3 className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                  </div>
                </div>
              </div>
            </div>

            {/* Questions List Header */}
            {questions.length > 0 && (
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4 sm:mb-6">
                <div>
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                    Questions ({questions.length})
                  </h3>
                  {questions.length > 0 && (
                    <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mt-1">
                      {questions.filter(q => q.category).length} tagged with AI • {expandedQuestions.size} expanded
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-3 sm:space-x-4 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  <button
                    onClick={() => setExpandedQuestions(new Set())}
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors px-2 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    Collapse All
                  </button>
                  <button
                    onClick={() => setExpandedQuestions(new Set(questions.map(q => q.id || '')))}
                    className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors px-2 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  >
                    Expand All
                  </button>
                </div>
              </div>
            )}

            {/* Questions Grid or Empty State */}
            {questions.length > 0 ? (
              <div className="flex flex-col lg:flex-row gap-4 sm:gap-6">
                {/* Main Content - Questions Grid */}
                <div className="flex-1 space-y-4 sm:space-y-6">
                  {/* Question Numbers Grid */}
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4">
                      <div>
                        <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">
                          <span className="hidden sm:inline">Click on a question number to view details:</span>
                          <span className="sm:hidden">Tap question numbers:</span>
                        </h4>
                        {selectedTopicFilter && (
                          <p className="text-xs sm:text-sm text-blue-600 dark:text-blue-400 mt-1">
                            Showing {questions.filter(q => selectedTopicFilter === 'untagged' ? !q.category : q.category === selectedTopicFilter).length} questions
                            {selectedTopicFilter === 'untagged' ? ' (untagged)' : ` for "${selectedTopicFilter}"`}
                          </p>
                        )}
                      </div>
                      {isTagging && (
                        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2 text-xs sm:text-sm text-purple-600 dark:text-purple-400">
                          <div className="flex items-center space-x-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            {taggingProgress.total > 0 ? (
                              <span className="hidden sm:inline">AI is analyzing questions... ({taggingProgress.current}/{taggingProgress.total})</span>
                            ) : (
                              <span>AI is analyzing questions...</span>
                            )}
                          </div>
                          {taggingProgress.total > 0 && (
                            <div className="w-full sm:w-20 bg-purple-200 dark:bg-purple-800 rounded-full h-2">
                              <div
                                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${(taggingProgress.current / taggingProgress.total) * 100}%` }}
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  <div className="grid grid-cols-4 xs:grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-14 gap-2 sm:gap-3 lg:gap-4">
                    {questions
                      .filter(question => !selectedTopicFilter || question.category === selectedTopicFilter || (selectedTopicFilter === 'untagged' && !question.category))
                      .map((question) => {
                        const originalIndex = questions.findIndex(q => q.id === question.id);
                        const isExpanded = expandedQuestions.has(question.id || '');
                        const topicColors = getTopicColor(question.category);

                        return (
                          <button
                            key={question.id}
                            onClick={() => toggleQuestionExpansion(question.id || '')}
                            className={`
                              relative w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 rounded-lg sm:rounded-xl font-bold text-xs sm:text-sm transition-all duration-200 transform active:scale-95 sm:hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 border-2 shadow-md hover:shadow-lg touch-manipulation
                              ${isExpanded
                                ? `${topicColors.bg} ${topicColors.border} ${topicColors.text} shadow-lg ring-2 ring-green-300 dark:ring-green-500`
                                : question.category
                                  ? `${topicColors.bg} ${topicColors.border} ${topicColors.text} ${topicColors.hover}`
                                  : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-600'
                              }
                            `}
                            title={`Question ${originalIndex + 1}: ${question.question.substring(0, 50)}...${question.category ? ` | Topic: ${question.category}` : ''}`}
                          >
                            {originalIndex + 1}
                            {isExpanded && (
                              <div className="absolute -top-1 -right-1 w-3 h-3 sm:w-4 sm:h-4 bg-yellow-400 rounded-full flex items-center justify-center">
                                <svg className="w-1.5 h-1.5 sm:w-2 sm:h-2 text-yellow-800" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                </svg>
                              </div>
                            )}
                          </button>
                        );
                      })}
                  </div>
                </div>

                {/* Expanded Questions Details */}
                {Array.from(expandedQuestions).length > 0 && (
                  <div className="space-y-3 sm:space-y-4">
                    <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-white">
                      Expanded Questions:
                    </h4>
                    {questions
                      .filter(question => expandedQuestions.has(question.id || ''))
                      .map((question) => {
                        const questionIndex = questions.findIndex(q => q.id === question.id);
                        return (
                          <div
                            key={question.id}
                            className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-lg overflow-hidden"
                          >
                            {/* Question Header */}
                            <div className="bg-gradient-to-r from-green-600 to-emerald-600 px-4 sm:px-6 py-3 sm:py-4 flex items-center justify-between">
                              <div className="flex items-center space-x-2 sm:space-x-3">
                                <div className="w-6 h-6 sm:w-8 sm:h-8 bg-white/20 rounded-full flex items-center justify-center">
                                  <span className="text-white font-bold text-xs sm:text-sm">{questionIndex + 1}</span>
                                </div>
                                <h5 className="text-white font-semibold text-sm sm:text-base">Question {questionIndex + 1}</h5>
                              </div>
                              <button
                                onClick={() => toggleQuestionExpansion(question.id || '')}
                                className="text-white/80 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10 touch-manipulation"
                                title="Close question"
                              >
                                <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </button>
                            </div>

                            {/* Question Content */}
                            <div className="p-4 sm:p-6">
                              <p className="text-gray-900 dark:text-white text-base sm:text-lg mb-4 leading-relaxed">
                                {question.question}
                              </p>

                              {/* Tags and Category Display */}
                              {(question.category || (question.tags && question.tags.length > 0)) && (
                                <div className="mb-6 space-y-2">
                                  {question.category && (
                                    <div className="flex items-center space-x-2">
                                      <Tag className="h-4 w-4 text-purple-600" />
                                      <span className="text-sm font-medium text-purple-700 dark:text-purple-300">
                                        Topic: {question.category}
                                      </span>
                                    </div>
                                  )}
                                  {question.tags && question.tags.length > 0 && (
                                    <div className="flex flex-wrap gap-2">
                                      {question.tags.map((tag, tagIndex) => (
                                        <span
                                          key={tagIndex}
                                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300"
                                        >
                                          {tag}
                                        </span>
                                      ))}
                                    </div>
                                  )}
                                </div>
                              )}

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div className={`p-4 rounded-lg transition-colors ${
                                  question.correctAnswer === 'A'
                                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 font-semibold border-2 border-green-300 dark:border-green-600'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                                }`}>
                                  <span className="font-bold">A)</span> {question.choiceA}
                                </div>
                                <div className={`p-4 rounded-lg transition-colors ${
                                  question.correctAnswer === 'B'
                                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 font-semibold border-2 border-green-300 dark:border-green-600'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                                }`}>
                                  <span className="font-bold">B)</span> {question.choiceB}
                                </div>
                                <div className={`p-4 rounded-lg transition-colors ${
                                  question.correctAnswer === 'C'
                                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 font-semibold border-2 border-green-300 dark:border-green-600'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                                }`}>
                                  <span className="font-bold">C)</span> {question.choiceC}
                                </div>
                                <div className={`p-4 rounded-lg transition-colors ${
                                  question.correctAnswer === 'D'
                                    ? 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 font-semibold border-2 border-green-300 dark:border-green-600'
                                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300'
                                }`}>
                                  <span className="font-bold">D)</span> {question.choiceD}
                                </div>
                              </div>

                              {question.explanation && (
                                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border-l-4 border-blue-400">
                                  <p className="text-blue-800 dark:text-blue-200">
                                    <span className="font-semibold">Explanation:</span> {question.explanation}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        );
                      })}
                  </div>
                )}
                </div>

                {/* Legend Sidebar - Mobile: Below content, Desktop: Right sidebar */}
                <div className="w-full lg:w-80 lg:flex-shrink-0">
                  <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-lg lg:sticky lg:top-6">
                    <div className="flex items-center justify-between mb-3 sm:mb-4">
                      <h4 className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white flex items-center">
                        <Tag className="h-4 w-4 mr-2" />
                        <span className="hidden sm:inline">GDPR Topics</span>
                        <span className="sm:hidden">Topics</span>
                      </h4>
                      {selectedTopicFilter && (
                        <button
                          onClick={() => setSelectedTopicFilter(null)}
                          className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors px-2 py-1 rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 touch-manipulation"
                        >
                          Clear Filter
                        </button>
                      )}
                    </div>

                    {/* Get unique topics from questions */}
                    {(() => {
                      const uniqueTopics = Array.from(new Set(questions.map(q => q.category).filter(Boolean)));
                      const untaggedCount = questions.filter(q => !q.category).length;

                      return (
                        <div className="space-y-2 sm:space-y-3 max-h-64 sm:max-h-96 overflow-y-auto">
                          {uniqueTopics.map((topic) => {
                            const topicColors = getTopicColor(topic);
                            const count = questions.filter(q => q.category === topic).length;
                            const isSelected = selectedTopicFilter === topic;

                            return (
                              <button
                                key={topic}
                                onClick={() => setSelectedTopicFilter(isSelected ? null : topic as string)}
                                className={`w-full flex items-center space-x-2 sm:space-x-3 text-xs p-2 sm:p-3 rounded-lg transition-all duration-200 hover:shadow-md touch-manipulation ${
                                  isSelected
                                    ? `${topicColors.bg} ${topicColors.border} border-2 shadow-md`
                                    : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
                                }`}
                              >
                                <div className={`w-4 h-4 sm:w-5 sm:h-5 rounded-full border-2 ${topicColors.bg} ${topicColors.border} flex-shrink-0 shadow-sm`}></div>
                                <div className="flex-1 min-w-0 text-left">
                                  <p className={`font-medium truncate text-xs sm:text-sm ${isSelected ? topicColors.text : 'text-gray-900 dark:text-white'}`} title={topic}>
                                    {topic}
                                  </p>
                                  <p className={`text-xs ${isSelected ? topicColors.text + ' opacity-80' : 'text-gray-500 dark:text-gray-400'}`}>
                                    {count} question{count !== 1 ? 's' : ''}
                                  </p>
                                </div>
                                {isSelected && (
                                  <div className="flex-shrink-0">
                                    <svg className={`w-3 h-3 sm:w-4 sm:h-4 ${topicColors.text}`} fill="currentColor" viewBox="0 0 20 20">
                                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                  </div>
                                )}
                              </button>
                            );
                          })}

                          {untaggedCount > 0 && (
                            <button
                              onClick={() => setSelectedTopicFilter(selectedTopicFilter === 'untagged' ? null : 'untagged')}
                              className={`w-full flex items-center space-x-3 text-xs p-3 rounded-lg transition-all duration-200 hover:shadow-md border-t pt-4 mt-3 ${
                                selectedTopicFilter === 'untagged'
                                  ? 'bg-gray-100 dark:bg-gray-700 border-2 border-gray-300 dark:border-gray-600 shadow-md'
                                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/50 border border-transparent'
                              }`}
                            >
                              <div className="w-5 h-5 rounded-full border-2 bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 flex-shrink-0 shadow-sm"></div>
                              <div className="flex-1 text-left">
                                <p className={`font-medium ${selectedTopicFilter === 'untagged' ? 'text-gray-800 dark:text-gray-200' : 'text-gray-900 dark:text-white'}`}>
                                  Untagged
                                </p>
                                <p className={`${selectedTopicFilter === 'untagged' ? 'text-gray-600 dark:text-gray-400' : 'text-gray-500 dark:text-gray-400'}`}>
                                  {untaggedCount} question{untaggedCount !== 1 ? 's' : ''}
                                </p>
                              </div>
                              {selectedTopicFilter === 'untagged' && (
                                <div className="flex-shrink-0">
                                  <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                  </svg>
                                </div>
                              )}
                            </button>
                          )}

                          {uniqueTopics.length === 0 && untaggedCount === 0 && (
                            <p className="text-gray-500 dark:text-gray-400 text-xs text-center py-4">
                              No questions available
                            </p>
                          )}

                          {uniqueTopics.length === 0 && untaggedCount > 0 && (
                            <div className="text-center py-4">
                              <p className="text-gray-500 dark:text-gray-400 text-xs mb-2">
                                Questions not yet tagged
                              </p>
                              {(certificate.name.toLowerCase().includes('cipp') || certificate.name.toLowerCase().includes('gdpr')) && (
                                <p className="text-blue-600 dark:text-blue-400 text-xs">
                                  Use &quot;AI Tag Questions&quot; to classify them
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      );
                    })()}
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-12 sm:py-20">
                <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-6 sm:p-12 shadow-xl max-w-md mx-auto">
                  <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-3 sm:p-4 rounded-full w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6">
                    <HelpCircle className="h-6 w-6 sm:h-8 sm:w-8 text-white mx-auto" />
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                    No questions yet
                  </h3>
                  <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 mb-4 sm:mb-6">
                    Build your question bank to practice and test your knowledge
                  </p>
                  <div className="flex flex-col gap-3 justify-center">
                    <Button
                      onClick={() => setIsImportModalOpen(true)}
                      className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-sm"
                      size="sm"
                    >
                      <FileSpreadsheet className="h-4 w-4 mr-2" />
                      Import from Excel
                    </Button>
                    <Button className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-sm" size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Manually
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Excel Import Modal */}
        <ExcelImportModal
          isOpen={isImportModalOpen}
          onClose={() => setIsImportModalOpen(false)}
          onImport={handleImportQuestions}
          isImporting={isImporting}
        />
      </div>
    </div>
  );
}

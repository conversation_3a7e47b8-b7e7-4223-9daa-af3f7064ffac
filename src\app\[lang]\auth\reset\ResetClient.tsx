"use client";

import React, { useState } from "react";
import <PERSON> from "next/link";
import { z } from "zod";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { resetPassword } from "@/Firebase/Authentication/authConfig";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";

export default function ResetClient({
  dict
}: {
  dict: Dictionary
}) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      await resetPassword(data.email);
      toast({
        title: dict.auth.reset.success,
        variant: "default",
      });
      setEmailSent(true);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.reset.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthCard
      title={dict.auth.reset.title}
      description={dict.auth.reset.subtitle}
      footer={
        <div className="text-center mt-4">
          <Link
            href="signin"
            className="text-sm text-primary hover:underline"
          >
            {dict.auth.reset.backToSignIn}
          </Link>
        </div>
      }
    >
      {emailSent ? (
        <div className="text-center space-y-4">
          <div className="bg-muted p-4 rounded-md">
            <p className="text-sm">{dict.auth.reset.success}</p>
          </div>
          <Link
            href="signin"
            className="text-primary underline underline-offset-4 hover:text-primary/90"
          >
            {dict.auth.reset.backToSignIn}
          </Link>
        </div>
      ) : (
        <AuthForm
          schema={formSchema}
          onSubmit={onSubmit}
          submitText={dict.auth.reset.button}
          isLoading={isLoading}
        >
          <FormField
            name="email"
            label={dict.auth.common.email}
            placeholder="<EMAIL>"
            type="email"
            required
            autoComplete="email"
          />
        </AuthForm>
      )}
    </AuthCard>
  );
}

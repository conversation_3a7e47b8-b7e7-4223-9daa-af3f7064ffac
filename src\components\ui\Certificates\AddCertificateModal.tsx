"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Award, Calendar, FileText, Target, X } from "lucide-react";

interface AddCertificateModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CertificateData) => Promise<void>;
  isSubmitting?: boolean;
}

interface CertificateData {
  name: string;
  description: string;
  provider: string;
  targetDate: string;
  priority: 'low' | 'medium' | 'high'; // Fix type to match CreateCertificateData
  notes: string;
}

export function AddCertificateModal({ isOpen, onClose, onSubmit, isSubmitting = false }: AddCertificateModalProps) {
  const [formData, setFormData] = useState<CertificateData>({
    name: "",
    description: "",
    provider: "",
    targetDate: "",
    priority: "medium",
    notes: ""
  });

  const handleInputChange = (field: keyof CertificateData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim() || !formData.description.trim()) {
      return;
    }

    try {
      await onSubmit(formData);
      // Reset form
      setFormData({
        name: "",
        description: "",
        provider: "",
        targetDate: "",
        priority: "medium",
        notes: ""
      });
    } catch (error) {
      console.error("Error adding certificate:", error);
    }
  };

  const handleClose = () => {
    setFormData({
      name: "",
      description: "",
      provider: "",
      targetDate: "",
      priority: "medium",
      notes: ""
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-white dark:bg-gray-800 border-0 shadow-2xl">
        <DialogHeader className="pb-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg">
                <Award className="h-5 w-5 text-white" />
              </div>
              <DialogTitle className="text-xl font-bold text-gray-900 dark:text-white">
                Add New Certificate
              </DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6 pt-6">
          {/* Certificate Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
              <Award className="h-4 w-4 mr-2 text-blue-600" />
              Certificate Name *
            </Label>
            <Input
              id="name"
              type="text"
              placeholder="e.g., AWS Solutions Architect Associate"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
              <FileText className="h-4 w-4 mr-2 text-green-600" />
              Description *
            </Label>
            <Textarea
              id="description"
              placeholder="Describe what this certification covers and why you want to achieve it..."
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500 min-h-[100px]"
              required
            />
          </div>

          {/* Provider */}
          <div className="space-y-2">
            <Label htmlFor="provider" className="text-sm font-semibold text-gray-900 dark:text-white">
              Certification Provider
            </Label>
            <Input
              id="provider"
              type="text"
              placeholder="e.g., Amazon Web Services, Microsoft, Google Cloud"
              value={formData.provider}
              onChange={(e) => handleInputChange("provider", e.target.value)}
              className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Target Date and Priority */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="targetDate" className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
                <Calendar className="h-4 w-4 mr-2 text-purple-600" />
                Target Date
              </Label>
              <Input
                id="targetDate"
                type="date"
                value={formData.targetDate}
                onChange={(e) => handleInputChange("targetDate", e.target.value)}
                className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="priority" className="text-sm font-semibold text-gray-900 dark:text-white flex items-center">
                <Target className="h-4 w-4 mr-2 text-orange-600" />
                Priority
              </Label>
              <select
                id="priority"
                value={formData.priority}
                onChange={(e) => handleInputChange("priority", e.target.value)}
                className="w-full px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-md focus:ring-blue-500 focus:border-blue-500 text-gray-900 dark:text-white"
              >
                <option value="low">Low Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="high">High Priority</option>
              </select>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes" className="text-sm font-semibold text-gray-900 dark:text-white">
              Additional Notes
            </Label>
            <Textarea
              id="notes"
              placeholder="Any additional notes, study resources, or reminders..."
              value={formData.notes}
              onChange={(e) => handleInputChange("notes", e.target.value)}
              className="bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600 focus:ring-blue-500 focus:border-blue-500"
              rows={3}
            />
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
              className="px-6"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.name.trim() || !formData.description.trim()}
              className="px-6 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700"
            >
              {isSubmitting ? "Adding..." : "Add Certificate"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}

"use client";

import React, { useState } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  createExamAttempt,
  addQuestionsToAttempt,
  getExamAttempt,
  getExamQuestions,
  submitQuestionAnswer,
  completeExamAttempt,
  ExamAttempt,
  ExamQuestion
} from "@/Firebase/firestore/services/ExamService";
import { selectExamQuestions } from "@/Firebase/firestore/services/ExamUtilsService";
import { getCertificateQuestions } from "@/Firebase/firestore/services/QuestionsService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";

// Import exam components
import ExamInterface from "./exam/ExamInterface";
import ExamResults from "./exam/ExamResults";
import ExamAnalytics from "./exam/ExamAnalytics";
import ExamSetupOverhaul from "./exam/ExamSetupOverhaul";
import AnswerFullQuestionBank from "./exam/AnswerFullQuestionBank";

interface TakeExamTabProps {
  certificate: Certificate;
}

type ExamState = 'setup' | 'in_progress' | 'completed' | 'analytics' | 'answer_full_bank';

export default function TakeExamTab({ certificate }: TakeExamTabProps) {
  const [examState, setExamState] = useState<ExamState>('setup');
  const [currentAttempt, setCurrentAttempt] = useState<ExamAttempt | null>(null);
  const [examQuestions, setExamQuestions] = useState<ExamQuestion[]>([]);

  const { user } = useAuth();
  const { toast } = useToast();
  const handleStartExam = async (examType: 'practice' | 'normal', questionCount: number) => {
    if (!user?.uid || !certificate.id) return;

    try {
      // Get all questions for the certificate
      const allQuestions = await getCertificateQuestions(user.uid, certificate.id);

      if (allQuestions.length === 0) {
        toast({
          title: "No Questions Available",
          description: "Please add questions to the question bank first.",
          variant: "destructive",
        });
        return;
      }

      // Select questions for the exam
      const selectedQuestions = await selectExamQuestions(
        user.uid,
        certificate.id,
        allQuestions,
        questionCount
      );

      // Create exam attempt
      const attemptId = await createExamAttempt(
        user.uid,
        certificate.id,
        examType,
        questionCount
      );

      // Add questions to the attempt
      await addQuestionsToAttempt(attemptId, selectedQuestions);

      // Load the attempt and questions
      const [attempt, questions] = await Promise.all([
        getExamAttempt(attemptId),
        getExamQuestions(attemptId)
      ]);

      if (attempt && questions.length > 0) {
        setCurrentAttempt(attempt);
        setExamQuestions(questions);
        setExamState('in_progress');
      }

      toast({
        title: "Exam Started",
        description: `Your ${examType} exam with ${questionCount} questions has begun.`,
      });

    } catch (error) {
      console.error('Error starting exam:', error);
      toast({
        title: "Error",
        description: "Failed to start exam. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSubmitAnswer = async (attemptId: string, questionId: string, answer: 'A' | 'B' | 'C' | 'D', timeSpent: number): Promise<boolean> => {
    try {
      const isCorrect = await submitQuestionAnswer(attemptId, questionId, answer, timeSpent);

      // Update local state
      setExamQuestions(prev => prev.map(q =>
        q.id === questionId
          ? { ...q, userAnswer: answer, isCorrect, timeSpent }
          : q
      ));

      return isCorrect;
    } catch (error) {
      console.error('Error submitting answer:', error);
      toast({
        title: "Error",
        description: "Failed to submit answer. Please try again.",
        variant: "destructive",
      });
      return false;
    }
  };

  const handleCompleteExam = async () => {
    if (!currentAttempt?.id) return;

    try {
      const completedAttempt = await completeExamAttempt(currentAttempt.id);
      setCurrentAttempt(completedAttempt);
      setExamState('completed');

      toast({
        title: "Exam Completed",
        description: `You scored ${Math.round(completedAttempt.percentage || 0)}%!`,
      });
    } catch (error) {
      console.error('Error completing exam:', error);
      toast({
        title: "Error",
        description: "Failed to complete exam. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExitExam = () => {
    setExamState('setup');
    setCurrentAttempt(null);
    setExamQuestions([]);
  };

  const handleRetakeExam = () => {
    setExamState('setup');
    setCurrentAttempt(null);
    setExamQuestions([]);
  };

  const handleViewAnalytics = () => {
    setExamState('analytics');
  };

  const handleBackToSetup = () => {
    setExamState('setup');
    setCurrentAttempt(null);
    setExamQuestions([]);
  };

  const handleStartAnswerFullBank = () => {
    setExamState('answer_full_bank');
  };

  // Render different states
  switch (examState) {
    case 'setup':
      return (
        <ExamSetupOverhaul
          certificate={certificate}
          onStartExam={handleStartExam}
          onViewAnalytics={handleViewAnalytics}
          onStartAnswerFullBank={handleStartAnswerFullBank}
        />
      );

    case 'in_progress':
      if (!currentAttempt || examQuestions.length === 0) {
        return <div>Loading exam...</div>;
      }
      return (
        <ExamInterface
          attempt={currentAttempt}
          questions={examQuestions}
          onSubmitAnswer={handleSubmitAnswer}
          onCompleteExam={handleCompleteExam}
          onExitExam={handleExitExam}
        />
      );

    case 'completed':
      if (!currentAttempt || examQuestions.length === 0) {
        return <div>Loading results...</div>;
      }
      return (
        <ExamResults
          attempt={currentAttempt}
          questions={examQuestions}
          onRetakeExam={handleRetakeExam}
          onBackToSetup={handleBackToSetup}
          onViewAnalytics={handleViewAnalytics}
        />
      );

    case 'analytics':
      return (
        <ExamAnalytics
          certificate={certificate}
          onBack={handleBackToSetup}
        />
      );

    case 'answer_full_bank':
      return (
        <AnswerFullQuestionBank
          certificate={certificate}
          onBack={handleBackToSetup}
        />
      );

    default:
      return <div>Invalid exam state</div>;
  }
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { type ExamAnalytics as ExamAnalyticsType, generateExamAnalytics, getIncorrectlyAnsweredQuestions } from "@/Firebase/firestore/services/ExamUtilsService";
import { ExamAttempt } from "@/Firebase/firestore/services/ExamService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
// Removed unused UI components
import AttemptDetails from "./AttemptDetails";
import {
  TrendingUp,
  Trophy,
  Target,

  BarChart3,
  AlertTriangle,
  CheckCircle,
  ArrowLeft,
  Calendar,
  Loader2,
  Eye
} from "lucide-react";

interface ExamAnalyticsProps {
  certificate: Certificate;
  onBack: () => void;
}

export default function ExamAnalytics({ certificate, onBack }: ExamAnalyticsProps) {
  const [analytics, setAnalytics] = useState<ExamAnalyticsType | null>(null);
  const [incorrectQuestions, setIncorrectQuestions] = useState<Array<{ questionId: string; topic: string; attempts: number; correctAttempts: number }>>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedAttempt, setSelectedAttempt] = useState<ExamAttempt | null>(null);

  const { user } = useAuth();
  const { toast } = useToast();

  const loadAnalytics = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const [analyticsData, incorrectData] = await Promise.all([
        generateExamAnalytics(user.uid, certificate.id),
        getIncorrectlyAnsweredQuestions(user.uid, certificate.id)
      ]);

      setAnalytics(analyticsData);
      setIncorrectQuestions(incorrectData);
    } catch (error) {
      console.error('Error loading analytics:', error);
      toast({
        title: "Error",
        description: "Failed to load analytics. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    if (user?.uid && certificate.id) {
      loadAnalytics();
    }
  }, [user?.uid, certificate.id, loadAnalytics]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading analytics...</p>
        </div>
      </div>
    );
  }

  // Show attempt details if an attempt is selected
  if (selectedAttempt) {
    return (
      <AttemptDetails
        attempt={selectedAttempt}
        onBack={() => setSelectedAttempt(null)}
      />
    );
  }

  if (!analytics || analytics.totalAttempts === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
        <div className="max-w-4xl mx-auto px-6 py-12">
          <Button onClick={onBack} variant="ghost" className="mb-6">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exam Setup
          </Button>

          <div className="text-center py-20">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 rounded-full w-24 h-24 mx-auto mb-6">
              <BarChart3 className="h-12 w-12 text-white mx-auto" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              No Exam Data Yet
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Take your first exam to see detailed analytics and performance insights.
            </p>
            <Button onClick={onBack}>
              Take Your First Exam
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            onClick={onBack}
            className="mb-6 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 hover:from-gray-200 hover:to-gray-300 dark:hover:from-gray-600 dark:hover:to-gray-500 text-gray-800 dark:text-gray-100 border-0 shadow-lg hover:shadow-xl transition-all duration-300"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Exam Setup
          </Button>
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-3">
              📊 Exam Analytics
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              Detailed insights into your exam performance and progress
            </p>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-xl">
                <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                  {analytics.totalAttempts}
                </div>
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300">Total Attempts</div>
              </div>
            </div>
            <div className="text-xs text-blue-600/70 dark:text-blue-400/70">
              Keep practicing to improve your skills
            </div>
          </div>

          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <Trophy className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400">
                  {Math.round(analytics.bestScore)}%
                </div>
                <div className="text-sm font-medium text-green-700 dark:text-green-300">Best Score</div>
              </div>
            </div>
            <div className="text-xs text-green-600/70 dark:text-green-400/70">
              Your highest achievement so far
            </div>
          </div>

          <div className="bg-gradient-to-br from-purple-50 to-violet-50 dark:from-purple-950/30 dark:to-violet-950/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-xl">
                <TrendingUp className="h-6 w-6 text-purple-600 dark:text-purple-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-purple-600 dark:text-purple-400">
                  {Math.round(analytics.averageScore)}%
                </div>
                <div className="text-sm font-medium text-purple-700 dark:text-purple-300">Average Score</div>
              </div>
            </div>
            <div className="text-xs text-purple-600/70 dark:text-purple-400/70">
              Your overall performance trend
            </div>
          </div>

          <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div className="text-right">
                <div className="text-3xl font-bold text-orange-600 dark:text-orange-400">
                  {analytics.weakAreas.length}
                </div>
                <div className="text-sm font-medium text-orange-700 dark:text-orange-300">Weak Areas</div>
              </div>
            </div>
            <div className="text-xs text-orange-600/70 dark:text-orange-400/70">
              Topics that need more practice
            </div>
          </div>
        </div>

        {/* Topic Performance */}
        <div className="mb-10">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
            <div className="flex items-center space-x-3 mb-8">
              <div className="p-3 bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 rounded-xl">
                <BarChart3 className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Performance by Topic</h2>
                <p className="text-gray-600 dark:text-gray-400">See how you&apos;re performing across different subjects</p>
              </div>
            </div>

            {analytics.topicPerformance.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {analytics.topicPerformance.map((topic: { topic: string; correct: number; total: number; percentage: number }) => (
                  <div key={topic.topic} className="bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/50 dark:to-slate-900/50 rounded-xl p-6 border border-gray-200/50 dark:border-gray-700/50">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold text-gray-900 dark:text-white text-lg">
                        {topic.topic}
                      </h3>
                      <div className="flex items-center space-x-3">
                        <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          {topic.correct}/{topic.total}
                        </span>
                        <div className={`px-3 py-1 rounded-full text-sm font-bold ${
                          topic.percentage >= 80
                            ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                            : topic.percentage >= 60
                            ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {Math.round(topic.percentage)}%
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4 overflow-hidden">
                        <div
                          className={`h-4 rounded-full transition-all duration-1000 ease-out ${
                            topic.percentage >= 80
                              ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                              : topic.percentage >= 60
                              ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                              : 'bg-gradient-to-r from-red-500 to-pink-500'
                          }`}
                          style={{ width: `${topic.percentage}%` }}
                        />
                      </div>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-xs font-medium text-white drop-shadow-lg">
                          {topic.percentage >= 10 ? `${Math.round(topic.percentage)}%` : ''}
                        </span>
                      </div>
                    </div>

                    <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                      {topic.percentage >= 80 ? '🎉 Excellent mastery!' :
                       topic.percentage >= 60 ? '👍 Good progress' :
                       '📚 Needs more practice'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit mx-auto mb-4">
                  <BarChart3 className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 text-lg mb-2">No topic performance data available yet</p>
                <p className="text-gray-400 dark:text-gray-500 text-sm">Take more exams to see detailed topic analysis</p>
              </div>
            )}
          </div>
        </div>

        {/* Strengths and Weaknesses */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
          {/* Strong Areas */}
          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 rounded-2xl p-8 shadow-xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-xl">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-green-700 dark:text-green-300">💪 Strong Areas</h2>
                <p className="text-green-600/70 dark:text-green-400/70 text-sm">Topics you&apos;ve mastered</p>
              </div>
            </div>

            {analytics.strongAreas.length > 0 ? (
              <div className="space-y-3">
                {analytics.strongAreas.map((area: string) => (
                  <div key={area} className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-green-200/50 dark:border-green-800/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-green-800 dark:text-green-200">{area}</span>
                    </div>
                  </div>
                ))}
                <div className="mt-4 p-3 bg-green-100/50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300 font-medium">
                    🎉 Great job! Keep up the excellent work in these areas.
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="p-4 bg-green-100 dark:bg-green-900/30 rounded-full w-fit mx-auto mb-4">
                  <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
                </div>
                <p className="text-green-600 dark:text-green-400 font-medium mb-2">
                  Keep practicing to identify your strong areas!
                </p>
                <p className="text-green-500/70 dark:text-green-400/70 text-sm">
                  Take more exams to discover your strengths
                </p>
              </div>
            )}
          </div>

          {/* Weak Areas */}
          <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950/30 dark:to-red-950/30 rounded-2xl p-8 shadow-xl">
            <div className="flex items-center space-x-3 mb-6">
              <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-xl">
                <AlertTriangle className="h-6 w-6 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-orange-700 dark:text-orange-300">📚 Areas for Improvement</h2>
                <p className="text-orange-600/70 dark:text-orange-400/70 text-sm">Topics that need more practice</p>
              </div>
            </div>

            {analytics.weakAreas.length > 0 ? (
              <div className="space-y-3">
                {analytics.weakAreas.map((area: string) => (
                  <div key={area} className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-orange-200/50 dark:border-orange-800/50">
                    <div className="flex items-center space-x-3">
                      <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                      <span className="font-medium text-orange-800 dark:text-orange-200">{area}</span>
                    </div>
                  </div>
                ))}
                <div className="mt-4 p-3 bg-orange-100/50 dark:bg-orange-900/20 rounded-lg">
                  <p className="text-sm text-orange-700 dark:text-orange-300 font-medium">
                    💡 Focus on these topics in your next study session for better results.
                  </p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="p-4 bg-orange-100 dark:bg-orange-900/30 rounded-full w-fit mx-auto mb-4">
                  <Trophy className="h-8 w-8 text-orange-600 dark:text-orange-400" />
                </div>
                <p className="text-orange-600 dark:text-orange-400 font-medium mb-2">
                  Excellent! No weak areas identified.
                </p>
                <p className="text-orange-500/70 dark:text-orange-400/70 text-sm">
                  You&apos;re performing well across all topics
                </p>
              </div>
            )}
          </div>
        </div>

        {/* All Previous Attempts */}
        <div className="mb-10">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 shadow-xl">
            <div className="flex items-center justify-between mb-8">
              <div className="flex items-center space-x-3">
                <div className="p-3 bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-900/30 dark:to-indigo-900/30 rounded-xl">
                  <Calendar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-white">📅 Your Exam History</h2>
                  <p className="text-gray-600 dark:text-gray-400">Click on any attempt to see detailed results</p>
                </div>
              </div>
              <div className="bg-gradient-to-r from-purple-100 to-indigo-100 dark:from-purple-900/30 dark:to-indigo-900/30 px-4 py-2 rounded-full">
                <span className="text-sm font-bold text-purple-700 dark:text-purple-300">
                  {analytics.totalAttempts} total attempts
                </span>
              </div>
            </div>
            {analytics.recentAttempts.length > 0 ? (
              <div className="grid grid-cols-1 gap-4">
                {analytics.recentAttempts.map((attempt, index: number) => {
                  const scoreColor = (attempt.percentage || 0) >= 80
                    ? 'text-green-600 dark:text-green-400'
                    : (attempt.percentage || 0) >= 60
                    ? 'text-yellow-600 dark:text-yellow-400'
                    : 'text-red-600 dark:text-red-400';

                  const bgGradient = (attempt.percentage || 0) >= 80
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30'
                    : (attempt.percentage || 0) >= 60
                    ? 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/30 dark:to-orange-950/30'
                    : 'bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30';

                  const borderColor = (attempt.percentage || 0) >= 80
                    ? 'border-green-200/50 dark:border-green-800/50'
                    : (attempt.percentage || 0) >= 60
                    ? 'border-yellow-200/50 dark:border-yellow-800/50'
                    : 'border-red-200/50 dark:border-red-800/50';

                  return (
                    <div
                      key={attempt.id}
                      className={`group p-6 rounded-2xl border cursor-pointer transition-all duration-300 hover:shadow-xl hover:scale-[1.02] ${bgGradient} ${borderColor}`}
                      onClick={() => setSelectedAttempt(attempt)}
                    >
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-3">
                            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm px-3 py-1 rounded-full">
                              <span className="text-sm font-bold text-gray-700 dark:text-gray-300">
                                #{analytics.totalAttempts - index}
                              </span>
                            </div>
                            <div className={`px-3 py-1 rounded-full text-xs font-bold ${
                              attempt.examType === 'practice'
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                                : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                            }`}>
                              {attempt.examType === 'practice' ? '🎯 Practice' : '⚡ Normal'}
                            </div>
                          </div>
                          <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                            {attempt.completedAt?.toDate?.()?.toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            }) || 'Recently'}
                          </div>
                        </div>

                        <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm p-2 rounded-full">
                            <Eye className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${scoreColor}`}>
                            {Math.round(attempt.percentage || 0)}%
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Score ({attempt.score}/{attempt.totalQuestions})
                          </div>
                        </div>

                        {attempt.timeSpent && (
                          <div className="text-center">
                            <div className="text-lg font-bold text-gray-700 dark:text-gray-300">
                              {Math.floor(attempt.timeSpent / 60)}:{(attempt.timeSpent % 60).toString().padStart(2, '0')}
                            </div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Total Time
                            </div>
                          </div>
                        )}

                        <div className="text-center">
                          <div className="text-lg font-bold text-gray-700 dark:text-gray-300">
                            {Math.round((attempt.timeSpent || 0) / attempt.totalQuestions)}s
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Avg/Question
                          </div>
                        </div>
                      </div>

                      {/* Performance indicators */}
                      <div className="relative">
                        <div className="w-full bg-white/40 dark:bg-gray-700/40 rounded-full h-3 overflow-hidden">
                          <div
                            className={`h-3 rounded-full transition-all duration-1000 ease-out ${
                              (attempt.percentage || 0) >= 80
                                ? 'bg-gradient-to-r from-green-500 to-emerald-500'
                                : (attempt.percentage || 0) >= 60
                                ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                                : 'bg-gradient-to-r from-red-500 to-pink-500'
                            }`}
                            style={{ width: `${attempt.percentage || 0}%` }}
                          />
                        </div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <span className="text-xs font-medium text-white drop-shadow-lg">
                            {(attempt.percentage || 0) >= 15 ? `${Math.round(attempt.percentage || 0)}%` : ''}
                          </span>
                        </div>
                      </div>

                      <div className="mt-2 text-center">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {(attempt.percentage || 0) >= 80 ? '🎉 Excellent performance!' :
                           (attempt.percentage || 0) >= 60 ? '👍 Good job!' :
                           '📚 Keep practicing!'}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-full w-fit mx-auto mb-4">
                  <Calendar className="h-8 w-8 text-gray-400" />
                </div>
                <p className="text-gray-500 dark:text-gray-400 text-lg mb-2">No exam attempts yet</p>
                <p className="text-gray-400 dark:text-gray-500 text-sm">Take your first exam to see your history here</p>
              </div>
            )}
          </div>
        </div>

        {/* Frequently Missed Questions */}
        {incorrectQuestions.length > 0 && (
          <div className="mb-10">
            <div className="bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 rounded-2xl p-8 shadow-xl">
              <div className="flex items-center space-x-3 mb-8">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-xl">
                  <AlertTriangle className="h-6 w-6 text-red-600 dark:text-red-400" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-red-700 dark:text-red-300">🎯 Frequently Missed Topics</h2>
                  <p className="text-red-600/70 dark:text-red-400/70">Topics that need extra attention</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {incorrectQuestions.slice(0, 10).map((question, index) => (
                  <div key={question.questionId} className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-xl p-4 border border-red-200/50 dark:border-red-800/50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="bg-red-100 dark:bg-red-900/30 px-2 py-1 rounded-full">
                          <span className="text-sm font-bold text-red-600 dark:text-red-400">
                            #{index + 1}
                          </span>
                        </div>
                        <div className="bg-red-100 dark:bg-red-900/30 px-3 py-1 rounded-full">
                          <span className="text-sm font-medium text-red-700 dark:text-red-300">
                            {question.topic}
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-bold text-red-600 dark:text-red-400">
                          {question.correctAttempts}/{question.attempts}
                        </div>
                        <div className="text-xs text-red-500 dark:text-red-400">
                          correct
                        </div>
                      </div>
                    </div>

                    <div className="mt-3">
                      <div className="w-full bg-red-200/50 dark:bg-red-800/30 rounded-full h-2">
                        <div
                          className="h-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full transition-all duration-500"
                          style={{ width: `${(question.correctAttempts / question.attempts) * 100}%` }}
                        />
                      </div>
                      <div className="text-xs text-red-600/70 dark:text-red-400/70 mt-1">
                        {Math.round((question.correctAttempts / question.attempts) * 100)}% success rate
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-red-100/50 dark:bg-red-900/20 rounded-xl">
                <p className="text-sm text-red-700 dark:text-red-300 font-medium text-center">
                  💡 Focus on these topics in your next study session to improve your overall performance.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

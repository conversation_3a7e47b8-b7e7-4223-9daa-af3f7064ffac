import { 
  collection, 
  doc, 
  addDoc, 
  getDocs, 
  getDoc, 
  updateDoc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';
import { Question } from './QuestionsService';

export interface ExamAttempt {
  id?: string;
  userId: string;
  certificateId: string;
  examType: 'practice' | 'normal';
  totalQuestions: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  score?: number;
  percentage?: number;
  timeSpent?: number; // in seconds
  startedAt: Timestamp;
  completedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ExamQuestion {
  id?: string;
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  userAnswer?: 'A' | 'B' | 'C' | 'D';
  isCorrect?: boolean;
  timeSpent?: number; // in seconds
  topic?: string;
  explanation?: string;
  orderIndex: number;
  answeredAt?: Timestamp;
}

export interface ExamAnalytics {
  totalAttempts: number;
  averageScore: number;
  bestScore: number;
  topicPerformance: Array<{
    topic: string;
    correct: number;
    total: number;
    percentage: number;
  }>;
  recentAttempts: ExamAttempt[];
  weakAreas: string[];
  strongAreas: string[];
}

const ATTEMPTS_COLLECTION = 'examAttempts';
const QUESTIONS_SUBCOLLECTION = 'questions';

/**
 * Create a new exam attempt
 */
export const createExamAttempt = async (
  userId: string,
  certificateId: string,
  examType: 'practice' | 'normal',
  totalQuestions: number
): Promise<string> => {
  try {
    const attemptData: Omit<ExamAttempt, 'id'> = {
      userId,
      certificateId,
      examType,
      totalQuestions,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(collection(firestore, ATTEMPTS_COLLECTION), attemptData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating exam attempt:', error);
    throw new Error('Failed to create exam attempt');
  }
};

/**
 * Add questions to an exam attempt as a subcollection
 */
export const addQuestionsToAttempt = async (
  attemptId: string,
  questions: Question[]
): Promise<void> => {
  try {
    const batch = writeBatch(firestore);
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);

    questions.forEach((question, index) => {
      const questionDocRef = doc(questionsRef);
      const examQuestionData: Omit<ExamQuestion, 'id'> = {
        questionId: question.id!,
        questionText: question.question,
        choiceA: question.choiceA,
        choiceB: question.choiceB,
        choiceC: question.choiceC,
        choiceD: question.choiceD,
        correctAnswer: question.correctAnswer,
        topic: question.category,
        orderIndex: index,
        ...(question.explanation && { explanation: question.explanation }),
      };
      batch.set(questionDocRef, examQuestionData);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error adding questions to attempt:', error);
    throw new Error('Failed to add questions to attempt');
  }
};

/**
 * Submit answer for a question in subcollection
 */
export const submitQuestionAnswer = async (
  attemptId: string,
  examQuestionId: string,
  userAnswer: 'A' | 'B' | 'C' | 'D',
  timeSpent: number
): Promise<boolean> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionRef = doc(attemptRef, QUESTIONS_SUBCOLLECTION, examQuestionId);
    const questionSnap = await getDoc(questionRef);

    if (!questionSnap.exists()) {
      throw new Error('Exam question not found');
    }

    const examQuestion = questionSnap.data() as ExamQuestion;
    const isCorrect = userAnswer === examQuestion.correctAnswer;

    await updateDoc(questionRef, {
      userAnswer,
      isCorrect,
      timeSpent,
      answeredAt: serverTimestamp(),
    });

    return isCorrect;
  } catch (error) {
    console.error('Error submitting question answer:', error);
    throw new Error('Failed to submit answer');
  }
};

/**
 * Complete an exam attempt
 */
export const completeExamAttempt = async (attemptId: string): Promise<ExamAttempt> => {
  try {
    // Get all questions for this attempt from subcollection
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const questionsSnap = await getDocs(questionsRef);
    const questions = questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as ExamQuestion));

    // Calculate score
    const answeredQuestions = questions.filter(q => q.userAnswer);
    const correctAnswers = questions.filter(q => q.isCorrect === true);
    const score = correctAnswers.length;
    const percentage = answeredQuestions.length > 0 ? (score / answeredQuestions.length) * 100 : 0;

    // Calculate total time spent
    const timeSpent = questions.reduce((total, q) => total + (q.timeSpent || 0), 0);

    // Update attempt
    await updateDoc(attemptRef, {
      status: 'completed',
      score,
      percentage,
      timeSpent,
      completedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });

    // Return updated attempt
    const updatedAttemptSnap = await getDoc(attemptRef);
    return { id: updatedAttemptSnap.id, ...updatedAttemptSnap.data() } as ExamAttempt;
  } catch (error) {
    console.error('Error completing exam attempt:', error);
    throw new Error('Failed to complete exam attempt');
  }
};

/**
 * Get exam attempt by ID
 */
export const getExamAttempt = async (attemptId: string): Promise<ExamAttempt | null> => {
  try {
    const attemptSnap = await getDoc(doc(firestore, ATTEMPTS_COLLECTION, attemptId));
    if (attemptSnap.exists()) {
      return { id: attemptSnap.id, ...attemptSnap.data() } as ExamAttempt;
    }
    return null;
  } catch (error) {
    console.error('Error getting exam attempt:', error);
    throw new Error('Failed to get exam attempt');
  }
};

/**
 * Get questions for an exam attempt from subcollection
 */
export const getExamQuestions = async (attemptId: string): Promise<ExamQuestion[]> => {
  try {
    const attemptRef = doc(firestore, ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, QUESTIONS_SUBCOLLECTION);
    const questionsQuery = query(questionsRef, orderBy('orderIndex', 'asc'));
    const questionsSnap = await getDocs(questionsQuery);
    return questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as ExamQuestion));
  } catch (error) {
    console.error('Error getting exam questions:', error);
    throw new Error('Failed to get exam questions');
  }
};

/**
 * Get user's exam attempts for a certificate
 */
export const getUserExamAttempts = async (
  userId: string,
  certificateId: string
): Promise<ExamAttempt[]> => {
  try {
    const attemptsQuery = query(
      collection(firestore, ATTEMPTS_COLLECTION),
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      orderBy('createdAt', 'desc')
    );
    const attemptsSnap = await getDocs(attemptsQuery);
    return attemptsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as ExamAttempt));
  } catch (error) {
    console.error('Error getting user exam attempts:', error);
    throw new Error('Failed to get exam attempts');
  }
};

import {
  collection,
  doc,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';


export interface RepairCenterExamAttempt {
  id?: string;
  userId: string;
  certificateId: string;
  examType: 'repair_center' | 'topic_explanation' | 'ai_generated';
  gameType?: 'flashcards' | 'multiple_choice' | 'true_false' | 'fill_blank' | 'matching' | 'concept_mapping';
  totalQuestions: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  score?: number;
  percentage?: number;
  timeSpent?: number; // in seconds
  startedAt: Timestamp;
  completedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  sourceQuestionIds: string[]; // Original repair center question IDs
  topics: string[]; // Topics covered in this exam
}

export interface RepairCenterExamQuestion {
  id?: string;
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  userAnswer?: 'A' | 'B' | 'C' | 'D';
  isCorrect?: boolean;
  timeSpent?: number; // in seconds
  topic?: string;
  explanation?: string;
  orderIndex: number;
  answeredAt?: Timestamp;
  userExplanation?: string; // User's explanation for their answer
  aiAnalysis?: {
    isExplanationCorrect: boolean;
    feedback: string;
    suggestions: string[];
    score: number; // 1-10 scale
    analyzedAt: Timestamp;
  };
  priority?: 'super-critical' | 'critical' | 'moderate' | 'low';
  originalSuccessRate?: number;
}

export interface TopicExplanationQuestion {
  id?: string;
  originalQuestionId: string;
  originalQuestion: {
    question: string;
    choiceA: string;
    choiceB: string;
    choiceC: string;
    choiceD: string;
    correctAnswer: 'A' | 'B' | 'C' | 'D';
    category?: string;
  };
  userAnswer?: 'A' | 'B' | 'C' | 'D';
  userExplanation?: string;
  initialAnalysis?: {
    understandingScore: number; // 1-10 scale
    isAnswerCorrect: boolean;
    mainTopicsAssessed: string[];
    feedback: string;
    knowledgeGaps: string[];
    shouldProceedToNext: boolean;
    analyzedAt: Timestamp;
  };
  followUpQuestion?: {
    type: 'why_correct' | 'why_incorrect' | 'scenario_application';
    question: string;
    expectedPoints: string[];
    minimizedTopicHint: string;
  };
  followUpAnswer?: string;
  followUpAnalysis?: {
    followUpScore: number; // 1-10 scale
    overallUnderstanding: number;
    feedback: string;
    conceptsMastered: string[];
    areasForImprovement: string[];
    mainTopicsAssessed: string[];
    readyForNextQuestion: boolean;
    studyRecommendations: string[];
    analyzedAt: Timestamp;
  };
  timeSpent?: number;
  answeredAt?: Timestamp;
  completedAt?: Timestamp;
  status: 'initial' | 'follow_up' | 'completed';
}

export interface GameTypeContent {
  flashcards: {
    front: string;
    back: string;
    topic: string;
    difficulty: 'easy' | 'medium' | 'hard';
  }[];
  multiple_choice: RepairCenterExamQuestion[];
  true_false: {
    statement: string;
    isTrue: boolean;
    explanation: string;
    topic: string;
  }[];
  fill_blank: {
    sentence: string;
    blanks: string[];
    options: string[];
    topic: string;
  }[];
  matching: {
    leftItems: string[];
    rightItems: string[];
    correctMatches: { left: number; right: number }[];
    topic: string;
  }[];
  concept_mapping: {
    centralConcept: string;
    relatedConcepts: string[];
    relationships: { from: string; to: string; relationship: string }[];
    topic: string;
  }[];
}

const REPAIR_EXAM_ATTEMPTS_COLLECTION = 'repairCenterExamAttempts';
const REPAIR_EXAM_QUESTIONS_SUBCOLLECTION = 'questions';
const TOPIC_EXPLANATIONS_SUBCOLLECTION = 'topicExplanations';

/**
 * Create a new repair center exam attempt
 */
export const createRepairCenterExamAttempt = async (
  userId: string,
  certificateId: string,
  examType: 'repair_center' | 'topic_explanation' | 'ai_generated',
  totalQuestions: number,
  sourceQuestionIds: string[],
  topics: string[],
  gameType?: 'flashcards' | 'multiple_choice' | 'true_false' | 'fill_blank' | 'matching' | 'concept_mapping'
): Promise<string> => {
  try {
    const attemptData: Omit<RepairCenterExamAttempt, 'id'> = {
      userId,
      certificateId,
      examType,
      gameType,
      totalQuestions,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
      sourceQuestionIds,
      topics
    };

    const docRef = await addDoc(collection(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION), attemptData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating repair center exam attempt:', error);
    throw new Error('Failed to create repair center exam attempt');
  }
};

/**
 * Add questions to a repair center exam attempt
 */
export const addQuestionsToRepairCenterAttempt = async (
  attemptId: string,
  questions: Omit<RepairCenterExamQuestion, 'id'>[]
): Promise<void> => {
  try {
    const batch = writeBatch(firestore);
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, REPAIR_EXAM_QUESTIONS_SUBCOLLECTION);

    questions.forEach((question) => {
      const questionRef = doc(questionsRef);
      batch.set(questionRef, question);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error adding questions to repair center exam attempt:', error);
    throw new Error('Failed to add questions to repair center exam attempt');
  }
};

/**
 * Submit an answer for a repair center exam question
 */
export const submitRepairCenterAnswer = async (
  attemptId: string,
  questionId: string,
  answer: 'A' | 'B' | 'C' | 'D',
  timeSpent: number,
  userExplanation?: string
): Promise<boolean> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, REPAIR_EXAM_QUESTIONS_SUBCOLLECTION);
    const questionRef = doc(questionsRef, questionId);
    
    const questionDoc = await getDoc(questionRef);
    if (!questionDoc.exists()) {
      throw new Error('Question not found');
    }
    
    const questionData = questionDoc.data() as RepairCenterExamQuestion;
    const isCorrect = questionData.correctAnswer === answer;
    
    const updateData: Partial<RepairCenterExamQuestion> = {
      userAnswer: answer,
      isCorrect,
      timeSpent,
      answeredAt: serverTimestamp() as Timestamp,
    };

    if (userExplanation) {
      updateData.userExplanation = userExplanation;
    }
    
    await updateDoc(questionRef, updateData);
    return isCorrect;
  } catch (error) {
    console.error('Error submitting repair center answer:', error);
    throw new Error('Failed to submit repair center answer');
  }
};

/**
 * Get repair center exam attempt
 */
export const getRepairCenterExamAttempt = async (attemptId: string): Promise<RepairCenterExamAttempt | null> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const attemptDoc = await getDoc(attemptRef);
    
    if (!attemptDoc.exists()) {
      return null;
    }
    
    return { id: attemptDoc.id, ...attemptDoc.data() } as RepairCenterExamAttempt;
  } catch (error) {
    console.error('Error getting repair center exam attempt:', error);
    throw new Error('Failed to get repair center exam attempt');
  }
};

/**
 * Get questions for a repair center exam attempt
 */
export const getRepairCenterExamQuestions = async (attemptId: string): Promise<RepairCenterExamQuestion[]> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, REPAIR_EXAM_QUESTIONS_SUBCOLLECTION);
    const questionsQuery = query(questionsRef, orderBy('orderIndex', 'asc'));
    const questionsSnap = await getDocs(questionsQuery);
    return questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as RepairCenterExamQuestion));
  } catch (error) {
    console.error('Error getting repair center exam questions:', error);
    throw new Error('Failed to get repair center exam questions');
  }
};

/**
 * Complete a repair center exam attempt
 */
export const completeRepairCenterExamAttempt = async (attemptId: string): Promise<RepairCenterExamAttempt> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, REPAIR_EXAM_QUESTIONS_SUBCOLLECTION);
    const questionsSnap = await getDocs(questionsRef);
    const questions = questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as RepairCenterExamQuestion));

    const answeredQuestions = questions.filter(q => q.userAnswer);
    const correctAnswers = questions.filter(q => q.isCorrect === true);
    const score = correctAnswers.length;
    const percentage = answeredQuestions.length > 0 ? (score / answeredQuestions.length) * 100 : 0;

    const totalTimeSpent = questions.reduce((total, q) => total + (q.timeSpent || 0), 0);

    const updateData: Partial<RepairCenterExamAttempt> = {
      status: 'completed',
      score,
      percentage,
      timeSpent: totalTimeSpent,
      completedAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    await updateDoc(attemptRef, updateData);

    const updatedAttempt = await getDoc(attemptRef);
    return { id: updatedAttempt.id, ...updatedAttempt.data() } as RepairCenterExamAttempt;
  } catch (error) {
    console.error('Error completing repair center exam attempt:', error);
    throw new Error('Failed to complete repair center exam attempt');
  }
};

/**
 * Get user's repair center exam attempts
 */
export const getUserRepairCenterExamAttempts = async (
  userId: string,
  certificateId: string
): Promise<RepairCenterExamAttempt[]> => {
  try {
    const attemptsRef = collection(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION);
    const attemptsQuery = query(
      attemptsRef,
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      orderBy('createdAt', 'desc')
    );
    const attemptsSnap = await getDocs(attemptsQuery);
    return attemptsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as RepairCenterExamAttempt));
  } catch (error) {
    console.error('Error getting user repair center exam attempts:', error);
    throw new Error('Failed to get user repair center exam attempts');
  }
};

/**
 * Add AI analysis to a repair center exam question
 */
export const addAIAnalysisToQuestion = async (
  attemptId: string,
  questionId: string,
  analysis: {
    isExplanationCorrect: boolean;
    feedback: string;
    suggestions: string[];
    score: number;
  }
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, REPAIR_EXAM_QUESTIONS_SUBCOLLECTION);
    const questionRef = doc(questionsRef, questionId);

    const aiAnalysis = {
      ...analysis,
      analyzedAt: serverTimestamp() as Timestamp,
    };

    await updateDoc(questionRef, { aiAnalysis });
  } catch (error) {
    console.error('Error adding AI analysis to question:', error);
    throw new Error('Failed to add AI analysis to question');
  }
};

/**
 * Create topic explanation questions
 */
export const createTopicExplanationAttempt = async (
  userId: string,
  certificateId: string,
  topics: string[]
): Promise<string> => {
  try {
    const attemptData: Omit<RepairCenterExamAttempt, 'id'> = {
      userId,
      certificateId,
      examType: 'topic_explanation',
      totalQuestions: topics.length,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
      sourceQuestionIds: [],
      topics
    };

    const docRef = await addDoc(collection(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION), attemptData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating topic explanation attempt:', error);
    throw new Error('Failed to create topic explanation attempt');
  }
};

/**
 * Add topic explanation questions to attempt
 */
export const addTopicExplanationQuestions = async (
  attemptId: string,
  topicQuestions: Omit<TopicExplanationQuestion, 'id'>[]
): Promise<void> => {
  try {
    const batch = writeBatch(firestore);
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);

    topicQuestions.forEach((question) => {
      const questionRef = doc(topicQuestionsRef);
      batch.set(questionRef, question);
    });

    await batch.commit();
  } catch (error) {
    console.error('Error adding topic explanation questions:', error);
    throw new Error('Failed to add topic explanation questions');
  }
};

/**
 * Submit topic explanation answer
 */
export const submitTopicExplanationAnswer = async (
  attemptId: string,
  questionId: string,
  userExplanation: string,
  timeSpent: number
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const updateData: Partial<TopicExplanationQuestion> = {
      userExplanation,
      timeSpent,
      answeredAt: serverTimestamp() as Timestamp,
    };

    await updateDoc(questionRef, updateData);
  } catch (error) {
    console.error('Error submitting topic explanation answer:', error);
    throw new Error('Failed to submit topic explanation answer');
  }
};

/**
 * Add AI analysis to topic explanation
 */
export const addAIAnalysisToTopicExplanation = async (
  attemptId: string,
  questionId: string,
  analysis: {
    score: number;
    feedback: string;
    missingPoints: string[];
    correctPoints: string[];
    suggestions: string[];
  }
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const aiAnalysis = {
      ...analysis,
      analyzedAt: serverTimestamp() as Timestamp,
    };

    await updateDoc(questionRef, { aiAnalysis });
  } catch (error) {
    console.error('Error adding AI analysis to topic explanation:', error);
    throw new Error('Failed to add AI analysis to topic explanation');
  }
};

/**
 * Get topic explanation questions for an attempt
 */
export const getTopicExplanationQuestions = async (attemptId: string): Promise<TopicExplanationQuestion[]> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionsSnap = await getDocs(topicQuestionsRef);
    return questionsSnap.docs.map(doc => ({ id: doc.id, ...doc.data() } as TopicExplanationQuestion));
  } catch (error) {
    console.error('Error getting topic explanation questions:', error);
    throw new Error('Failed to get topic explanation questions');
  }
};

/**
 * Submit initial answer and explanation for topic explanation
 */
export const submitInitialTopicAnswer = async (
  attemptId: string,
  questionId: string,
  userAnswer: 'A' | 'B' | 'C' | 'D',
  userExplanation: string,
  timeSpent: number
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const updateData: Partial<TopicExplanationQuestion> = {
      userAnswer,
      userExplanation,
      timeSpent,
      answeredAt: serverTimestamp() as Timestamp,
      status: 'initial'
    };

    await updateDoc(questionRef, updateData);
  } catch (error) {
    console.error('Error submitting initial topic answer:', error);
    throw new Error('Failed to submit initial topic answer');
  }
};

/**
 * Add initial AI analysis to topic explanation
 */
export const addInitialAnalysisToTopicExplanation = async (
  attemptId: string,
  questionId: string,
  analysis: {
    understandingScore: number;
    isAnswerCorrect: boolean;
    mainTopicsAssessed: string[];
    feedback: string;
    knowledgeGaps: string[];
    shouldProceedToNext: boolean;
  },
  followUpQuestion?: {
    type: 'why_correct' | 'why_incorrect' | 'scenario_application';
    question: string;
    expectedPoints: string[];
    minimizedTopicHint: string;
  }
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const updateData: Partial<TopicExplanationQuestion> = {
      initialAnalysis: {
        ...analysis,
        analyzedAt: serverTimestamp() as Timestamp,
      },
      status: followUpQuestion ? 'follow_up' : 'completed'
    };

    if (followUpQuestion) {
      updateData.followUpQuestion = followUpQuestion;
    }

    if (analysis.shouldProceedToNext && analysis.understandingScore >= 5) {
      updateData.completedAt = serverTimestamp() as Timestamp;
      updateData.status = 'completed';
    }

    await updateDoc(questionRef, updateData);
  } catch (error) {
    console.error('Error adding initial analysis to topic explanation:', error);
    throw new Error('Failed to add initial analysis to topic explanation');
  }
};

/**
 * Submit follow-up answer for topic explanation
 */
export const submitFollowUpAnswer = async (
  attemptId: string,
  questionId: string,
  followUpAnswer: string,
  timeSpent: number
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const updateData: Partial<TopicExplanationQuestion> = {
      followUpAnswer,
      timeSpent: (timeSpent || 0) + (timeSpent || 0), // Add to existing time
    };

    await updateDoc(questionRef, updateData);
  } catch (error) {
    console.error('Error submitting follow-up answer:', error);
    throw new Error('Failed to submit follow-up answer');
  }
};

/**
 * Add follow-up AI analysis to topic explanation
 */
export const addFollowUpAnalysisToTopicExplanation = async (
  attemptId: string,
  questionId: string,
  analysis: {
    followUpScore: number;
    overallUnderstanding: number;
    feedback: string;
    conceptsMastered: string[];
    areasForImprovement: string[];
    mainTopicsAssessed: string[];
    readyForNextQuestion: boolean;
    studyRecommendations: string[];
  }
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, REPAIR_EXAM_ATTEMPTS_COLLECTION, attemptId);
    const topicQuestionsRef = collection(attemptRef, TOPIC_EXPLANATIONS_SUBCOLLECTION);
    const questionRef = doc(topicQuestionsRef, questionId);

    const updateData: Partial<TopicExplanationQuestion> = {
      followUpAnalysis: {
        ...analysis,
        analyzedAt: serverTimestamp() as Timestamp,
      },
      status: 'completed',
      completedAt: serverTimestamp() as Timestamp
    };

    await updateDoc(questionRef, updateData);
  } catch (error) {
    console.error('Error adding follow-up analysis to topic explanation:', error);
    throw new Error('Failed to add follow-up analysis to topic explanation');
  }
};

/**
 * Mark a question as mastered in repair center (remove from repair list)
 */
export const markQuestionAsMastered = async (
  userId: string,
  certificateId: string,
  questionId: string,
  masteryData: {
    masteredAt: Timestamp;
    finalScore: number;
    attempts: number;
    masteryMethod: 'topic_explanation' | 'practice_exam' | 'review';
  }
): Promise<void> => {
  try {
    // This would typically update a user's mastered questions collection
    // For now, we'll create a simple mastered questions tracking
    const masteredRef = collection(firestore, 'userMasteredQuestions');
    await addDoc(masteredRef, {
      userId,
      certificateId,
      questionId,
      ...masteryData
    });
  } catch (error) {
    console.error('Error marking question as mastered:', error);
    throw new Error('Failed to mark question as mastered');
  }
};

/**
 * Get user's mastered questions for a certificate
 */
export const getUserMasteredQuestions = async (
  userId: string,
  certificateId: string
): Promise<string[]> => {
  try {
    const masteredRef = collection(firestore, 'userMasteredQuestions');
    const masteredQuery = query(
      masteredRef,
      where('userId', '==', userId),
      where('certificateId', '==', certificateId)
    );
    const masteredSnap = await getDocs(masteredQuery);
    return masteredSnap.docs.map(doc => doc.data().questionId);
  } catch (error) {
    console.error('Error getting user mastered questions:', error);
    return [];
  }
};

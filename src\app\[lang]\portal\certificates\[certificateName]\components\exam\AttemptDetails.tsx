"use client";

import React, { useState, useEffect, useCallback } from "react";
import { ExamAttempt, ExamQuestion, getExamQuestions } from "@/Firebase/firestore/services/ExamService";
import { calculateTimeMetrics } from "@/Firebase/firestore/services/ExamUtilsService";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Target,
  Loader2,
  Trophy
} from "lucide-react";

interface AttemptDetailsProps {
  attempt: ExamAttempt;
  onBack: () => void;
}

export default function AttemptDetails({ attempt, onBack }: AttemptDetailsProps) {
  const [questions, setQuestions] = useState<ExamQuestion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'correct' | 'incorrect'>('all');
  
  const { toast } = useToast();

  const loadAttemptQuestions = useCallback(async () => {
    if (!attempt.id) return;

    try {
      setIsLoading(true);
      const attemptQuestions = await getExamQuestions(attempt.id);
      setQuestions(attemptQuestions);
    } catch (error) {
      console.error('Error loading attempt questions:', error);
      toast({
        title: "Error",
        description: "Failed to load attempt details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [attempt.id, toast]);

  useEffect(() => {
    if (attempt.id) {
      loadAttemptQuestions();
    }
  }, [attempt.id, loadAttemptQuestions]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading attempt details...</p>
        </div>
      </div>
    );
  }

  const answeredQuestions = questions.filter(q => q.userAnswer);
  const correctAnswers = questions.filter(q => q.isCorrect === true);
  const incorrectAnswers = questions.filter(q => q.isCorrect === false);
  const timeMetrics = calculateTimeMetrics(questions);

  const filteredQuestions = questions.filter(q => {
    if (selectedFilter === 'correct') return q.isCorrect === true;
    if (selectedFilter === 'incorrect') return q.isCorrect === false;
    return q.userAnswer; // Show all answered questions
  });

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 dark:text-green-400';
    if (percentage >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      <div className="max-w-6xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <Button onClick={onBack} variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Analytics
            </Button>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Attempt Details
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {attempt.completedAt?.toDate?.()?.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              }) || 'Recently completed'}
            </p>
          </div>
          <Badge 
            variant={attempt.examType === 'practice' ? 'default' : 'secondary'}
            className="text-lg px-4 py-2"
          >
            {attempt.examType === 'practice' ? 'Practice' : 'Normal'} Mode
          </Badge>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6 text-center">
              <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-full w-12 h-12 mx-auto mb-4">
                <Trophy className="h-6 w-6 text-blue-600 mx-auto" />
              </div>
              <div className={`text-2xl font-bold mb-1 ${getScoreColor(attempt.percentage || 0)}`}>
                {Math.round(attempt.percentage || 0)}%
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Final Score</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-full w-12 h-12 mx-auto mb-4">
                <CheckCircle className="h-6 w-6 text-green-600 mx-auto" />
              </div>
              <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                {correctAnswers.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Correct</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="bg-red-100 dark:bg-red-900/30 p-3 rounded-full w-12 h-12 mx-auto mb-4">
                <XCircle className="h-6 w-6 text-red-600 mx-auto" />
              </div>
              <div className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
                {incorrectAnswers.length}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Incorrect</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <div className="bg-purple-100 dark:bg-purple-900/30 p-3 rounded-full w-12 h-12 mx-auto mb-4">
                <Clock className="h-6 w-6 text-purple-600 mx-auto" />
              </div>
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                {Math.floor(timeMetrics.totalTime / 60)}m
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Time</div>
            </CardContent>
          </Card>
        </div>

        {/* Filter Buttons */}
        <div className="flex items-center space-x-4 mb-6">
          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Filter questions:</span>
          <div className="flex space-x-2">
            {[
              { key: 'all', label: `All (${answeredQuestions.length})`, color: 'bg-gray-100 text-gray-700 hover:bg-gray-200' },
              { key: 'correct', label: `Correct (${correctAnswers.length})`, color: 'bg-green-100 text-green-700 hover:bg-green-200' },
              { key: 'incorrect', label: `Incorrect (${incorrectAnswers.length})`, color: 'bg-red-100 text-red-700 hover:bg-red-200' }
            ].map((filter) => (
              <Button
                key={filter.key}
                variant="ghost"
                size="sm"
                onClick={() => setSelectedFilter(filter.key as 'all' | 'correct' | 'incorrect')}
                className={`${filter.color} ${
                  selectedFilter === filter.key 
                    ? 'ring-2 ring-offset-2 ring-blue-500' 
                    : ''
                }`}
              >
                {filter.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Questions List */}
        <div className="space-y-4">
          {filteredQuestions.map((question) => {
            const questionNumber = questions.findIndex(q => q.id === question.id) + 1;
            const isCorrect = question.isCorrect === true;
            
            return (
              <Card key={question.id} className={`border-l-4 ${
                isCorrect 
                  ? 'border-l-green-500 bg-green-50/50 dark:bg-green-950/20' 
                  : 'border-l-red-500 bg-red-50/50 dark:bg-red-950/20'
              }`}>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center text-sm font-medium ${
                        isCorrect 
                          ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300' 
                          : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {questionNumber}
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          Question {questionNumber}
                        </h3>
                        {question.topic && (
                          <Badge variant="outline" className="text-xs mt-1">
                            {question.topic}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      {isCorrect ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                      {question.timeSpent && (
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {question.timeSpent}s
                        </span>
                      )}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-900 dark:text-white mb-4 leading-relaxed">
                    {question.questionText}
                  </p>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-4">
                    {(['A', 'B', 'C', 'D'] as const).map((choice) => {
                      const isUserAnswer = question.userAnswer === choice;
                      const isCorrectAnswer = question.correctAnswer === choice;
                      
                      let bgColor = 'bg-gray-50 dark:bg-gray-800';
                      let textColor = 'text-gray-700 dark:text-gray-300';
                      let borderColor = 'border-gray-200 dark:border-gray-700';
                      
                      if (isCorrectAnswer) {
                        bgColor = 'bg-green-100 dark:bg-green-900/30';
                        textColor = 'text-green-800 dark:text-green-200';
                        borderColor = 'border-green-300 dark:border-green-700';
                      } else if (isUserAnswer && !isCorrect) {
                        bgColor = 'bg-red-100 dark:bg-red-900/30';
                        textColor = 'text-red-800 dark:text-red-200';
                        borderColor = 'border-red-300 dark:border-red-700';
                      }
                      
                      return (
                        <div
                          key={choice}
                          className={`p-3 rounded-lg border ${bgColor} ${borderColor}`}
                        >
                          <div className="flex items-start space-x-2">
                            <span className={`font-medium ${textColor}`}>
                              {choice})
                            </span>
                            <span className={`flex-1 ${textColor}`}>
                              {question[`choice${choice}` as keyof ExamQuestion] as string}
                            </span>
                            {isCorrectAnswer && (
                              <CheckCircle className="h-4 w-4 text-green-600 flex-shrink-0" />
                            )}
                            {isUserAnswer && !isCorrect && (
                              <XCircle className="h-4 w-4 text-red-600 flex-shrink-0" />
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                  
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <span className="text-gray-600 dark:text-gray-400">
                        Your answer: <span className={`font-medium ${isCorrect ? 'text-green-600' : 'text-red-600'}`}>
                          {question.userAnswer}
                        </span>
                      </span>
                      <span className="text-gray-600 dark:text-gray-400">
                        Correct answer: <span className="font-medium text-green-600">
                          {question.correctAnswer}
                        </span>
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {filteredQuestions.length === 0 && (
          <div className="text-center py-12">
            <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              No questions found for the selected filter
            </p>
          </div>
        )}
      </div>
    </div>
  );
}

import {
  collection,
  doc,
  addDoc,
  getDocs,
  getDoc,
  updateDoc,
  query,
  where,
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

export interface Question {
  id?: string;
  certificateId: string;
  userId: string;
  question: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  explanation?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  category?: string;
  tags?: string[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  isActive: boolean;
  orderIndex?: number; // Add order index to maintain Excel order
}

export interface CreateQuestionData {
  question: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  explanation?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
  category?: string;
  tags?: string[];
}

export interface ExcelQuestionRow {
  question: string;
  correctAnswer: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
}

const COLLECTION_NAME = 'certificates';
const QUESTIONS_SUBCOLLECTION = 'questions';

/**
 * Create a new question for a certificate
 */
export const createQuestion = async (
  userId: string,
  certificateId: string,
  questionData: CreateQuestionData
): Promise<string> => {
  try {
    const questionsRef = collection(
      firestore, 
      COLLECTION_NAME, 
      certificateId, 
      QUESTIONS_SUBCOLLECTION
    );
    
    const newQuestion: Omit<Question, 'id'> = {
      certificateId,
      userId,
      ...questionData,
      isActive: true,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(questionsRef, newQuestion);
    return docRef.id;
  } catch (error) {
    console.error('Error creating question:', error);
    throw new Error('Failed to create question');
  }
};

/**
 * Get all questions for a certificate
 */
export const getCertificateQuestions = async (
  userId: string,
  certificateId: string
): Promise<Question[]> => {
  try {
    const questionsRef = collection(
      firestore,
      COLLECTION_NAME,
      certificateId,
      QUESTIONS_SUBCOLLECTION
    );

    // Simplified query to avoid index requirements
    const q = query(
      questionsRef,
      where('userId', '==', userId),
      where('isActive', '==', true)
    );

    const querySnapshot = await getDocs(q);

    // Sort in memory instead of using orderBy to avoid index requirement
    const questions = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as Question));

    // Sort by orderIndex first (to maintain Excel order), then by createdAt
    return questions.sort((a, b) => {
      // If both have orderIndex, sort by orderIndex
      if (a.orderIndex !== undefined && b.orderIndex !== undefined) {
        return a.orderIndex - b.orderIndex;
      }
      // If only one has orderIndex, prioritize it
      if (a.orderIndex !== undefined) return -1;
      if (b.orderIndex !== undefined) return 1;
      // If neither has orderIndex, sort by createdAt (newest first)
      const aTime = a.createdAt?.toDate?.()?.getTime() || 0;
      const bTime = b.createdAt?.toDate?.()?.getTime() || 0;
      return bTime - aTime;
    });
  } catch (error) {
    console.error('Error fetching certificate questions:', error);
    throw new Error('Failed to fetch questions');
  }
};

/**
 * Import questions from Excel data
 */
export const importQuestionsFromExcel = async (
  userId: string,
  certificateId: string,
  excelData: ExcelQuestionRow[],
  importMode: 'replace' | 'append' = 'append'
): Promise<{ success: number; failed: number; errors: string[] }> => {
  try {
    const batch = writeBatch(firestore);
    const questionsRef = collection(
      firestore, 
      COLLECTION_NAME, 
      certificateId, 
      QUESTIONS_SUBCOLLECTION
    );

    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    // If replace mode, first mark all existing questions as inactive
    if (importMode === 'replace') {
      const existingQuestions = await getCertificateQuestions(userId, certificateId);
      existingQuestions.forEach(question => {
        if (question.id) {
          const questionRef = doc(questionsRef, question.id);
          batch.update(questionRef, { 
            isActive: false, 
            updatedAt: serverTimestamp() 
          });
        }
      });
    }

    // Process each row from Excel
    for (let i = 0; i < excelData.length; i++) {
      const row = excelData[i];
      
      try {
        // Validate required fields
        if (!row.question?.trim() || !row.correctAnswer?.trim()) {
          errors.push(`Row ${i + 1}: Question and correct answer are required`);
          failed++;
          continue;
        }

        // Find which choice matches the correct answer and convert to letter
        const choices = [
          { letter: 'A' as const, text: row.choiceA?.trim() || '' },
          { letter: 'B' as const, text: row.choiceB?.trim() || '' },
          { letter: 'C' as const, text: row.choiceC?.trim() || '' },
          { letter: 'D' as const, text: row.choiceD?.trim() || '' }
        ];

        const correctAnswerText = row.correctAnswer.trim();
        let correctAnswerLetter: 'A' | 'B' | 'C' | 'D' | null = null;

        // First, check if the correct answer is already a letter (A, B, C, or D)
        if (['A', 'B', 'C', 'D'].includes(correctAnswerText.toUpperCase())) {
          correctAnswerLetter = correctAnswerText.toUpperCase() as 'A' | 'B' | 'C' | 'D';
        } else {
          // If not a letter, find which choice text matches the correct answer
          const matchingChoice = choices.find(choice =>
            choice.text.toLowerCase() === correctAnswerText.toLowerCase()
          );
          if (matchingChoice) {
            correctAnswerLetter = matchingChoice.letter;
          }
        }

        if (!correctAnswerLetter) {
          errors.push(`Row ${i + 1}: Correct answer "${correctAnswerText}" must be either A, B, C, D or match one of the choice texts`);
          failed++;
          continue;
        }

        const newQuestionRef = doc(questionsRef);
        const questionData: Omit<Question, 'id'> = {
          certificateId,
          userId,
          question: row.question.trim(),
          correctAnswer: correctAnswerLetter,
          choiceA: row.choiceA?.trim() || '',
          choiceB: row.choiceB?.trim() || '',
          choiceC: row.choiceC?.trim() || '',
          choiceD: row.choiceD?.trim() || '',
          isActive: true,
          orderIndex: i, // Maintain Excel row order
          createdAt: serverTimestamp() as Timestamp,
          updatedAt: serverTimestamp() as Timestamp,
        };

        batch.set(newQuestionRef, questionData);
        success++;
      } catch (error) {
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        failed++;
      }
    }

    // Commit the batch
    await batch.commit();

    return { success, failed, errors };
  } catch (error) {
    console.error('Error importing questions from Excel:', error);
    throw new Error('Failed to import questions from Excel');
  }
};

/**
 * Export questions to Excel format
 */
export const exportQuestionsToExcel = async (
  userId: string,
  certificateId: string
): Promise<ExcelQuestionRow[]> => {
  try {
    const questions = await getCertificateQuestions(userId, certificateId);

    return questions.map(question => ({
      question: question.question,
      correctAnswer: question.correctAnswer, // This will be A, B, C, or D
      choiceA: question.choiceA,
      choiceB: question.choiceB,
      choiceC: question.choiceC,
      choiceD: question.choiceD,
    }));
  } catch (error) {
    console.error('Error exporting questions to Excel:', error);
    throw new Error('Failed to export questions to Excel');
  }
};

/**
 * Update a question
 */
export const updateQuestion = async (
  userId: string,
  certificateId: string,
  questionId: string,
  updateData: Partial<CreateQuestionData>
): Promise<void> => {
  try {
    const questionRef = doc(
      firestore, 
      COLLECTION_NAME, 
      certificateId, 
      QUESTIONS_SUBCOLLECTION, 
      questionId
    );
    
    // Verify the question belongs to the user
    const questionSnap = await getDoc(questionRef);
    if (!questionSnap.exists()) {
      throw new Error('Question not found');
    }
    
    const questionData = questionSnap.data() as Question;
    if (questionData.userId !== userId) {
      throw new Error('Unauthorized access to question');
    }
    
    await updateDoc(questionRef, {
      ...updateData,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error updating question:', error);
    throw new Error('Failed to update question');
  }
};

/**
 * Update question tags in bulk
 */
export const updateQuestionTags = async (
  userId: string,
  certificateId: string,
  questionTags: Array<{ questionId: string; tags: string[]; topic?: string }>
): Promise<{ success: number; failed: number; errors: string[] }> => {
  try {
    const batch = writeBatch(firestore);
    const questionsRef = collection(
      firestore,
      COLLECTION_NAME,
      certificateId,
      QUESTIONS_SUBCOLLECTION
    );

    let success = 0;
    let failed = 0;
    const errors: string[] = [];

    for (const { questionId, tags, topic } of questionTags) {
      try {
        const questionRef = doc(questionsRef, questionId);

        // Verify the question exists and belongs to the user
        const questionSnap = await getDoc(questionRef);
        if (!questionSnap.exists()) {
          errors.push(`Question ${questionId}: Not found`);
          failed++;
          continue;
        }

        const questionData = questionSnap.data() as Question;
        if (questionData.userId !== userId) {
          errors.push(`Question ${questionId}: Unauthorized access`);
          failed++;
          continue;
        }

        // Update the question with new tags
        const updateData: {
          tags: string[];
          updatedAt: ReturnType<typeof serverTimestamp>;
          category?: string;
        } = {
          tags: tags,
          updatedAt: serverTimestamp(),
        };

        // Add topic as category if provided
        if (topic) {
          updateData.category = topic;
        }

        batch.update(questionRef, updateData);
        success++;
      } catch (error) {
        errors.push(`Question ${questionId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        failed++;
      }
    }

    // Commit the batch
    await batch.commit();

    return { success, failed, errors };
  } catch (error) {
    console.error('Error updating question tags:', error);
    throw new Error('Failed to update question tags');
  }
};

/**
 * Delete a question (soft delete by marking as inactive)
 */
export const deleteQuestion = async (
  userId: string,
  certificateId: string,
  questionId: string
): Promise<void> => {
  try {
    const questionRef = doc(
      firestore,
      COLLECTION_NAME,
      certificateId,
      QUESTIONS_SUBCOLLECTION,
      questionId
    );

    // Verify the question belongs to the user
    const questionSnap = await getDoc(questionRef);
    if (!questionSnap.exists()) {
      throw new Error('Question not found');
    }

    const questionData = questionSnap.data() as Question;
    if (questionData.userId !== userId) {
      throw new Error('Unauthorized access to question');
    }

    // Soft delete by marking as inactive
    await updateDoc(questionRef, {
      isActive: false,
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error deleting question:', error);
    throw new Error('Failed to delete question');
  }
};

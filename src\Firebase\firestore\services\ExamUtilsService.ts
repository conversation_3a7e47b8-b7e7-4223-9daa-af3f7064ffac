import { Question } from './QuestionsService';
import { ExamQuestion, ExamAnalytics, getUserExamAttempts } from './ExamService';

// Re-export ExamAnalytics for external use
export type { ExamAnalytics };
import { collection, getDocs, doc } from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

/**
 * Select questions for an exam with smart distribution and prioritization
 */
export const selectExamQuestions = async (
  userId: string,
  certificateId: string,
  allQuestions: Question[],
  requestedCount: number
): Promise<Question[]> => {
  try {
    // Get user's previous attempts to deprioritize previously used questions
    const previousAttempts = await getUserExamAttempts(userId, certificateId);
    const usedQuestionIds = new Set<string>();
    const questionUsageCount = new Map<string, number>();

    // Collect previously used questions from subcollections
    for (const attempt of previousAttempts) {
      if (attempt.status === 'completed' && attempt.id) {
        const attemptRef = doc(firestore, 'examAttempts', attempt.id);
        const questionsRef = collection(attemptRef, 'questions');
        const questionsSnap = await getDocs(questionsRef);

        questionsSnap.docs.forEach(doc => {
          const examQuestion = doc.data() as ExamQuestion;
          usedQuestionIds.add(examQuestion.questionId);
          questionUsageCount.set(
            examQuestion.questionId,
            (questionUsageCount.get(examQuestion.questionId) || 0) + 1
          );
        });
      }
    }

    // Group questions by topic for balanced distribution
    const questionsByTopic = new Map<string, Question[]>();
    allQuestions.forEach(question => {
      const topic = question.category || 'Untagged';
      if (!questionsByTopic.has(topic)) {
        questionsByTopic.set(topic, []);
      }
      questionsByTopic.get(topic)!.push(question);
    });

    // Calculate questions per topic (balanced distribution)
    const topics = Array.from(questionsByTopic.keys());
    const questionsPerTopic = Math.floor(requestedCount / topics.length);
    const remainingQuestions = requestedCount % topics.length;

    const selectedQuestions: Question[] = [];

    // Select questions from each topic
    topics.forEach((topic, topicIndex) => {
      const topicQuestions = questionsByTopic.get(topic)!;
      const targetCount = questionsPerTopic + (topicIndex < remainingQuestions ? 1 : 0);

      // Sort questions by usage count (least used first) and randomize within same usage count
      const sortedQuestions = topicQuestions
        .map(q => ({
          question: q,
          usageCount: questionUsageCount.get(q.id!) || 0,
          random: Math.random()
        }))
        .sort((a, b) => {
          if (a.usageCount !== b.usageCount) {
            return a.usageCount - b.usageCount; // Least used first
          }
          return a.random - b.random; // Random within same usage count
        })
        .map(item => item.question);

      // Take the required number of questions from this topic
      selectedQuestions.push(...sortedQuestions.slice(0, Math.min(targetCount, sortedQuestions.length)));
    });

    // If we don't have enough questions, fill with remaining questions
    if (selectedQuestions.length < requestedCount) {
      const selectedIds = new Set(selectedQuestions.map(q => q.id));
      const remainingAvailable = allQuestions.filter(q => !selectedIds.has(q.id));
      
      const additionalNeeded = requestedCount - selectedQuestions.length;
      const additionalQuestions = remainingAvailable
        .sort(() => Math.random() - 0.5) // Randomize
        .slice(0, additionalNeeded);
      
      selectedQuestions.push(...additionalQuestions);
    }

    // Final shuffle to mix topics
    return selectedQuestions
      .sort(() => Math.random() - 0.5)
      .slice(0, requestedCount);

  } catch (error) {
    console.error('Error selecting exam questions:', error);
    throw new Error('Failed to select exam questions');
  }
};

/**
 * Generate detailed analytics for a user's exam performance
 */
export const generateExamAnalytics = async (
  userId: string,
  certificateId: string
): Promise<ExamAnalytics> => {
  try {
    // Get all user attempts
    const attempts = await getUserExamAttempts(userId, certificateId);
    const completedAttempts = attempts.filter(a => a.status === 'completed');

    if (completedAttempts.length === 0) {
      return {
        totalAttempts: 0,
        averageScore: 0,
        bestScore: 0,
        topicPerformance: [],
        recentAttempts: [],
        weakAreas: [],
        strongAreas: []
      };
    }

    // Calculate basic stats
    const totalAttempts = completedAttempts.length;
    const scores = completedAttempts.map(a => a.percentage || 0);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const bestScore = Math.max(...scores);

    // Get all exam questions for completed attempts
    const topicStats = new Map<string, { correct: number; total: number }>();
    
    for (const attempt of completedAttempts) {
      if (attempt.id) {
        const attemptRef = doc(firestore, 'examAttempts', attempt.id);
        const questionsRef = collection(attemptRef, 'questions');
        const questionsSnap = await getDocs(questionsRef);

        questionsSnap.docs.forEach(doc => {
          const examQuestion = doc.data() as ExamQuestion;
          if (examQuestion.userAnswer) { // Only count answered questions
            const topic = examQuestion.topic || 'Untagged';

            if (!topicStats.has(topic)) {
              topicStats.set(topic, { correct: 0, total: 0 });
            }

            const stats = topicStats.get(topic)!;
            stats.total++;
            if (examQuestion.isCorrect) {
              stats.correct++;
            }
          }
        });
      }
    }

    // Generate topic performance
    const topicPerformance = Array.from(topicStats.entries())
      .map(([topic, stats]) => ({
        topic,
        correct: stats.correct,
        total: stats.total,
        percentage: (stats.correct / stats.total) * 100
      }))
      .sort((a, b) => b.percentage - a.percentage);

    // Identify weak and strong areas
    const weakAreas = topicPerformance
      .filter(tp => tp.percentage < 70 && tp.total >= 3) // At least 3 questions attempted
      .map(tp => tp.topic);

    const strongAreas = topicPerformance
      .filter(tp => tp.percentage >= 85 && tp.total >= 3)
      .map(tp => tp.topic);

    // Get recent attempts (last 5)
    const recentAttempts = completedAttempts.slice(0, 5);

    return {
      totalAttempts,
      averageScore,
      bestScore,
      topicPerformance,
      recentAttempts,
      weakAreas,
      strongAreas
    };

  } catch (error) {
    console.error('Error generating exam analytics:', error);
    throw new Error('Failed to generate exam analytics');
  }
};

/**
 * Get questions that were answered incorrectly in previous attempts
 */
export const getIncorrectlyAnsweredQuestions = async (
  userId: string,
  certificateId: string
): Promise<Array<{ questionId: string; topic: string; attempts: number; correctAttempts: number }>> => {
  try {
    const attempts = await getUserExamAttempts(userId, certificateId);
    const completedAttempts = attempts.filter(a => a.status === 'completed');

    const questionStats = new Map<string, { 
      topic: string; 
      attempts: number; 
      correctAttempts: number; 
    }>();

    for (const attempt of completedAttempts) {
      if (attempt.id) {
        const attemptRef = doc(firestore, 'examAttempts', attempt.id);
        const questionsRef = collection(attemptRef, 'questions');
        const questionsSnap = await getDocs(questionsRef);

        questionsSnap.docs.forEach(doc => {
          const examQuestion = doc.data() as ExamQuestion;
          if (examQuestion.userAnswer) {
            const questionId = examQuestion.questionId;

            if (!questionStats.has(questionId)) {
              questionStats.set(questionId, {
                topic: examQuestion.topic || 'Untagged',
                attempts: 0,
                correctAttempts: 0
              });
            }

            const stats = questionStats.get(questionId)!;
            stats.attempts++;
            if (examQuestion.isCorrect) {
              stats.correctAttempts++;
            }
          }
        });
      }
    }

    // Return questions that were answered incorrectly more often than correctly
    return Array.from(questionStats.entries())
      .filter(([, stats]) => stats.correctAttempts < stats.attempts / 2)
      .map(([questionId, stats]) => ({ questionId, ...stats }))
      .sort((a, b) => (a.correctAttempts / a.attempts) - (b.correctAttempts / b.attempts));

  } catch (error) {
    console.error('Error getting incorrectly answered questions:', error);
    throw new Error('Failed to get incorrectly answered questions');
  }
};

/**
 * Calculate time-based performance metrics
 */
export const calculateTimeMetrics = (examQuestions: ExamQuestion[]): {
  averageTimePerQuestion: number;
  fastestQuestion: number;
  slowestQuestion: number;
  totalTime: number;
} => {
  const answeredQuestions = examQuestions.filter(q => q.timeSpent && q.timeSpent > 0);
  
  if (answeredQuestions.length === 0) {
    return {
      averageTimePerQuestion: 0,
      fastestQuestion: 0,
      slowestQuestion: 0,
      totalTime: 0
    };
  }

  const times = answeredQuestions.map(q => q.timeSpent!);
  const totalTime = times.reduce((sum, time) => sum + time, 0);
  const averageTimePerQuestion = totalTime / times.length;
  const fastestQuestion = Math.min(...times);
  const slowestQuestion = Math.max(...times);

  return {
    averageTimePerQuestion,
    fastestQuestion,
    slowestQuestion,
    totalTime
  };
};

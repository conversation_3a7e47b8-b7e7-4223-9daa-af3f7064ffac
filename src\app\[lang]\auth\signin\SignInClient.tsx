"use client";

import React, { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { z } from "zod";
import { AuthCard } from "@/components/ui/authUI/AuthCard";
import { AuthForm } from "@/components/ui/authUI/AuthForm";
import { FormField } from "@/components/ui/authUI/FormField";
import { handleAuthError } from "@/Firebase/Authentication/errorHandler";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { useAuth } from "@/context";
import {
  GraduationCap,
  BookOpen,
  Target,
  BarChart3
} from "lucide-react";

export default function SignInClient({
  dict
}: {
  dict: Dictionary
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { toast } = useToast();
  const { login, loginWithGoogle, loading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Get current language from URL
  const lang = pathname.split('/')[1] || 'en';

  // Form schema
  const formSchema = z.object({
    email: z.string().email({
      message: dict.auth.common.invalidEmail,
    }),
    password: z.string().min(1, {
      message: dict.auth.common.requiredField,
    }),
    rememberMe: z.boolean().optional(),
  });

  // Handle form submission
  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    try {
      setIsLoading(true);
      // Sign in with email and password using our context
      await login(data.email, data.password);

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push(`/${lang}/portal/home`);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle Google sign-in
  const handleGoogleSignIn = async () => {
    try {
      setIsLoading(true);
      // Sign in with Google using our context
      await loginWithGoogle();

      toast({
        title: dict.auth.signin.success,
        variant: "default",
      });
      router.push(`/${lang}/portal/home`);
    } catch (error) {
      const errorMessage = handleAuthError(error);
      toast({
        title: dict.auth.signin.error,
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header Section */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-6">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full blur-lg opacity-30"></div>
              <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-4 rounded-full">
                <GraduationCap className="h-12 w-12 text-white" />
              </div>
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            Certification Portal
          </h1>
          <p className="text-gray-600 dark:text-gray-300 text-lg">
            Track your certification journey
          </p>
        </div>

        {/* Quick Stats Cards */}
        <div className="grid grid-cols-3 gap-3 mb-8">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20 shadow-lg">
            <BookOpen className="h-6 w-6 text-blue-500 mx-auto mb-2" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">Study</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Materials</div>
          </div>
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20 shadow-lg">
            <Target className="h-6 w-6 text-green-500 mx-auto mb-2" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">Practice</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Exams</div>
          </div>
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl p-4 text-center border border-white/20 shadow-lg">
            <BarChart3 className="h-6 w-6 text-purple-500 mx-auto mb-2" />
            <div className="text-sm font-medium text-gray-900 dark:text-white">Progress</div>
            <div className="text-xs text-gray-500 dark:text-gray-400">Tracking</div>
          </div>
        </div>

        {/* Sign In Form */}
        <AuthCard
          title="Welcome Back"
          description="Sign in to continue your certification journey"
          className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm shadow-2xl border-0 relative overflow-hidden"
          footer={
            <div className="text-center mt-6">
              <p className="text-sm text-muted-foreground">
                {dict.auth.common.dontHaveAccount}{" "}
                <Link
                  href="signup"
                  className="text-primary underline underline-offset-4 hover:text-primary/90 font-medium transition-colors"
                >
                  {dict.auth.signup.title}
                </Link>
              </p>
            </div>
          }
        >
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-indigo-500/10 to-blue-500/10 rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative">
            <AuthForm
              schema={formSchema}
              onSubmit={onSubmit}
              submitText="Access Portal"
              isLoading={isLoading || authLoading}
              googleSignIn={handleGoogleSignIn}
              googleText="Continue with Google"
            >
              <FormField
                name="email"
                label={dict.auth.common.email}
                placeholder="<EMAIL>"
                type="email"
                required
                autoComplete="email"
              />
              <FormField
                name="password"
                label={dict.auth.common.password}
                placeholder="Enter your password"
                type="password"
                required
                autoComplete="current-password"
              />
              <div className="flex items-center justify-between">
                <FormField
                  name="rememberMe"
                  isCheckbox
                  checkboxLabel={dict.auth.common.rememberMe}
                />
                <Link
                  href="reset"
                  className="text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                >
                  {dict.auth.common.forgotPassword}
                </Link>
              </div>
            </AuthForm>
          </div>
        </AuthCard>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Secure • Private • Focused on your success
          </p>
        </div>
      </div>
    </div>
  );
}

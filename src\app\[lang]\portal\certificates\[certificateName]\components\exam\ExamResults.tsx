"use client";

import React from "react";
import { ExamAttempt, ExamQuestion } from "@/Firebase/firestore/services/ExamService";
import { calculateTimeMetrics } from "@/Firebase/firestore/services/ExamUtilsService";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Trophy,
  Clock,
  Target,
  TrendingUp,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RotateCcw,
  Home,
  BarChart3
} from "lucide-react";

interface ExamResultsProps {
  attempt: ExamAttempt;
  questions: ExamQuestion[];
  onRetakeExam: () => void;
  onBackToSetup: () => void;
  onViewAnalytics: () => void;
}

export default function ExamResults({
  questions,
  onRetakeExam,
  onBackToSetup,
  onViewAnalytics
}: ExamResultsProps) {
  const answeredQuestions = questions.filter(q => q.userAnswer);
  const correctAnswers = questions.filter(q => q.isCorrect === true);
  const incorrectAnswers = questions.filter(q => q.isCorrect === false);
  
  const score = correctAnswers.length;
  const totalAnswered = answeredQuestions.length;
  const percentage = totalAnswered > 0 ? (score / totalAnswered) * 100 : 0;
  
  const timeMetrics = calculateTimeMetrics(questions);
  
  // Topic performance analysis
  const topicPerformance = questions.reduce((acc, q) => {
    if (q.userAnswer) {
      const topic = q.topic || 'Untagged';
      if (!acc[topic]) {
        acc[topic] = { correct: 0, total: 0 };
      }
      acc[topic].total++;
      if (q.isCorrect) {
        acc[topic].correct++;
      }
    }
    return acc;
  }, {} as Record<string, { correct: number; total: number }>);

  const topicStats = Object.entries(topicPerformance)
    .map(([topic, stats]) => ({
      topic,
      correct: stats.correct,
      total: stats.total,
      percentage: (stats.correct / stats.total) * 100
    }))
    .sort((a, b) => b.percentage - a.percentage);

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 dark:text-green-400';
    if (percentage >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getScoreBadge = (percentage: number) => {
    if (percentage >= 90) return { text: 'Excellent', color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' };
    if (percentage >= 80) return { text: 'Good', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' };
    if (percentage >= 70) return { text: 'Fair', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' };
    if (percentage >= 60) return { text: 'Pass', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' };
    return { text: 'Needs Improvement', color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' };
  };

  const scoreBadge = getScoreBadge(percentage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-blue-900/20">
      <div className="max-w-6xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-6 rounded-full w-24 h-24 mx-auto mb-6">
            <Trophy className="h-12 w-12 text-white mx-auto" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Exam Complete!
          </h1>
          <Badge className={`text-lg px-4 py-2 ${scoreBadge.color}`}>
            {scoreBadge.text}
          </Badge>
        </div>

        {/* Score Overview */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-6 w-6 mr-2" />
              Your Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className={`text-4xl font-bold mb-2 ${getScoreColor(percentage)}`}>
                  {Math.round(percentage)}%
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Overall Score</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-green-600 dark:text-green-400 mb-2">
                  {score}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Correct Answers</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-red-600 dark:text-red-400 mb-2">
                  {incorrectAnswers.length}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Incorrect Answers</div>
              </div>
              
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {totalAnswered}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Answered</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Time Analysis */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-6 w-6 mr-2" />
              Time Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {Math.floor(timeMetrics.totalTime / 60)}m {timeMetrics.totalTime % 60}s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Total Time</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                  {Math.round(timeMetrics.averageTimePerQuestion)}s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Avg per Question</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-2">
                  {timeMetrics.fastestQuestion}s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Fastest Question</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                  {timeMetrics.slowestQuestion}s
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-400">Slowest Question</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Topic Performance */}
        {topicStats.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-6 w-6 mr-2" />
                Performance by Topic
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topicStats.map((topic) => (
                  <div key={topic.topic} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-white">
                        {topic.topic}
                      </span>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          {topic.correct}/{topic.total}
                        </span>
                        <Badge 
                          variant="outline" 
                          className={topic.percentage >= 70 ? 'border-green-500 text-green-700' : 'border-red-500 text-red-700'}
                        >
                          {Math.round(topic.percentage)}%
                        </Badge>
                      </div>
                    </div>
                    <Progress 
                      value={topic.percentage} 
                      className={`h-2 ${topic.percentage >= 70 ? '[&>div]:bg-green-500' : '[&>div]:bg-red-500'}`}
                    />
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommendations */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-6 w-6 mr-2" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {percentage >= 80 ? (
                <div className="flex items-start p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-200">Great job!</p>
                    <p className="text-green-700 dark:text-green-300 text-sm">
                      You&apos;re well-prepared for this certification. Consider taking a few more practice exams to maintain your knowledge.
                    </p>
                  </div>
                </div>
              ) : percentage >= 60 ? (
                <div className="flex items-start p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <AlertTriangle className="h-5 w-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-yellow-800 dark:text-yellow-200">Good progress!</p>
                    <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                      You&apos;re on the right track. Focus on the topics where you scored below 70% and take more practice exams.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-start p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                  <XCircle className="h-5 w-5 text-red-600 mr-3 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium text-red-800 dark:text-red-200">More study needed</p>
                    <p className="text-red-700 dark:text-red-300 text-sm">
                      Consider reviewing the question bank and focusing on your weak areas before taking another exam.
                    </p>
                  </div>
                </div>
              )}

              {/* Weak areas */}
              {topicStats.filter(t => t.percentage < 70).length > 0 && (
                <div className="mt-4">
                  <p className="font-medium text-gray-900 dark:text-white mb-2">Focus on these topics:</p>
                  <div className="flex flex-wrap gap-2">
                    {topicStats
                      .filter(t => t.percentage < 70)
                      .map(topic => (
                        <Badge key={topic.topic} variant="outline" className="border-red-500 text-red-700">
                          {topic.topic} ({Math.round(topic.percentage)}%)
                        </Badge>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Question Review */}
        {incorrectAnswers.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center text-red-600 dark:text-red-400">
                <XCircle className="h-6 w-6 mr-2" />
                Questions You Got Wrong
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {questions
                  .filter(q => q.isCorrect === false)
                  .map((question) => {
                    const questionNumber = questions.findIndex(q => q.id === question.id) + 1;
                    return (
                      <div key={question.id} className="bg-red-50 dark:bg-red-950/30 rounded-xl p-4 border border-red-200 dark:border-red-800/50">
                        <div className="flex items-start space-x-3">
                          <div className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-lg px-2 py-1 text-sm font-medium flex-shrink-0">
                            Q{questionNumber}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-gray-900 dark:text-white font-medium mb-2 leading-relaxed">
                              {question.questionText}
                            </p>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-600 dark:text-gray-400">Your answer:</span>
                                <span className="bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 px-2 py-1 rounded font-medium">
                                  {question.userAnswer}) {question[`choice${question.userAnswer}` as keyof ExamQuestion] as string}
                                </span>
                              </div>
                              <div className="flex items-center space-x-2">
                                <span className="text-gray-600 dark:text-gray-400">Correct answer:</span>
                                <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-1 rounded font-medium">
                                  {question.correctAnswer}) {question[`choice${question.correctAnswer}` as keyof ExamQuestion] as string}
                                </span>
                              </div>
                            </div>
                            {question.topic && (
                              <div className="mt-2">
                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300">
                                  {question.topic}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Correct Answers Summary */}
        {correctAnswers.length > 0 && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center text-green-600 dark:text-green-400">
                <CheckCircle className="h-6 w-6 mr-2" />
                Questions You Got Right ({correctAnswers.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-2">
                {questions
                  .filter(q => q.isCorrect === true)
                  .map((question) => {
                    const questionNumber = questions.findIndex(q => q.id === question.id) + 1;
                    return (
                      <div key={question.id} className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-lg px-3 py-2 text-sm font-medium text-center">
                        Q{questionNumber}
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={onRetakeExam}
            size="lg"
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
          >
            <RotateCcw className="h-5 w-5 mr-2" />
            Retake Exam
          </Button>

          <Button
            onClick={onViewAnalytics}
            variant="outline"
            size="lg"
          >
            <BarChart3 className="h-5 w-5 mr-2" />
            View Full Analytics
          </Button>

          <Button
            onClick={onBackToSetup}
            variant="outline"
            size="lg"
          >
            <Home className="h-5 w-5 mr-2" />
            Back to Setup
          </Button>
        </div>
      </div>
    </div>
  );
}

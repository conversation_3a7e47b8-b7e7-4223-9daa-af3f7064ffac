import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/context';
import { useToast } from '@/components/ui/use-toast';
import {
  Certificate,
  CreateCertificateData,
  UpdateCertificateData,
  createCertificate,
  getUserCertificates,
  updateCertificate,
  deleteCertificate,
} from '@/Firebase/firestore/services/CertificatesService';

export function useCertificates() {
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const { user } = useAuth();
  const { toast } = useToast();

  const loadCertificates = useCallback(async () => {
    if (!user?.uid) return;

    try {
      setIsLoading(true);
      const userCertificates = await getUserCertificates(user.uid);
      setCertificates(userCertificates);
    } catch (error) {
      console.error('Error loading certificates:', error);
      toast({
        title: "Error",
        description: "Failed to load certificates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, toast]);

  // Load certificates when user changes
  useEffect(() => {
    if (user?.uid) {
      loadCertificates();
    } else {
      setCertificates([]);
      setIsLoading(false);
    }
  }, [user?.uid, loadCertificates]);

  const addCertificate = async (certificateData: CreateCertificateData) => {
    if (!user?.uid) {
      toast({
        title: "Error",
        description: "You must be logged in to add certificates.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await createCertificate(user.uid, certificateData);
      
      toast({
        title: "Success",
        description: "Certificate added successfully!",
        variant: "default",
      });
      
      // Reload certificates to get the latest data
      await loadCertificates();
    } catch (error) {
      console.error('Error adding certificate:', error);
      toast({
        title: "Error",
        description: "Failed to add certificate. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const editCertificate = async (certificateId: string, updateData: UpdateCertificateData) => {
    if (!user?.uid) {
      toast({
        title: "Error",
        description: "You must be logged in to edit certificates.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await updateCertificate(certificateId, user.uid, updateData);
      
      toast({
        title: "Success",
        description: "Certificate updated successfully!",
        variant: "default",
      });
      
      // Reload certificates to get the latest data
      await loadCertificates();
    } catch (error) {
      console.error('Error updating certificate:', error);
      toast({
        title: "Error",
        description: "Failed to update certificate. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  const removeCertificate = async (certificateId: string) => {
    if (!user?.uid) {
      toast({
        title: "Error",
        description: "You must be logged in to delete certificates.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await deleteCertificate(certificateId, user.uid);
      
      toast({
        title: "Success",
        description: "Certificate deleted successfully!",
        variant: "default",
      });
      
      // Reload certificates to get the latest data
      await loadCertificates();
    } catch (error) {
      console.error('Error deleting certificate:', error);
      toast({
        title: "Error",
        description: "Failed to delete certificate. Please try again.",
        variant: "destructive",
      });
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    certificates,
    isLoading,
    isSubmitting,
    addCertificate,
    editCertificate,
    removeCertificate,
    refreshCertificates: loadCertificates,
  };
}

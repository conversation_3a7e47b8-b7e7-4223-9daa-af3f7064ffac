import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!);



export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      topics,
      certificateName,
      gameType,
      difficulty = 'medium',
      itemCount = 10,
      repairCenterQuestions = [] // Array of actual questions user got wrong
    } = body;

    if (!topics || !Array.isArray(topics) || topics.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Topics array is required' },
        { status: 400 }
      );
    }

    if (!gameType) {
      return NextResponse.json(
        { success: false, error: 'Game type is required' },
        { status: 400 }
      );
    }

    const model = genAI.getGenerativeModel({ model: "gemini-2.5-pro" });

    const contextInfo = repairCenterQuestions.length > 0
      ? `\n\nCONTEXT - Specific questions the student got wrong in the Repair Center:\n${repairCenterQuestions.map((q: {
          question?: { question?: string; choiceA?: string; choiceB?: string; choiceC?: string; choiceD?: string; correctAnswer?: string; category?: string };
          questionText?: string;
          choiceA?: string;
          choiceB?: string;
          choiceC?: string;
          choiceD?: string;
          correctAnswer?: string;
          topic?: string;
          priority?: string;
          successRate?: number;
        }, index: number) =>
          `${index + 1}. Question: ${q.question?.question || q.questionText}
   A) ${q.question?.choiceA || q.choiceA}
   B) ${q.question?.choiceB || q.choiceB}
   C) ${q.question?.choiceC || q.choiceC}
   D) ${q.question?.choiceD || q.choiceD}
   Correct Answer: ${q.question?.correctAnswer || q.correctAnswer}
   Topic: ${q.question?.category || q.topic || 'Not specified'}
   Priority: ${q.priority || 'Not specified'}
   Success Rate: ${q.successRate ? q.successRate.toFixed(1) + '%' : 'Not available'}`
        ).join('\n\n')}`
      : '';

    let prompt = '';
    
    switch (gameType) {
      case 'flashcards':
        prompt = `
Create ${itemCount} flashcards for ${certificateName} based on the specific questions the student got wrong.

${contextInfo}

IMPORTANT: Base the flashcards directly on the concepts, terms, and knowledge areas tested in the above questions. Each flashcard should:
- Address specific concepts from the questions the student got wrong
- Help clarify the exact misconceptions that led to incorrect answers
- Focus on the key knowledge gaps revealed by their mistakes
- Be at ${difficulty} difficulty level
- Include practical applications relevant to the failed questions

Return as JSON:
{
  "gameType": "flashcards",
  "items": [
    {
      "id": "unique_id",
      "front": "Question or concept prompt (based on failed questions)",
      "back": "Answer or explanation (addressing the knowledge gap)",
      "topic": "specific topic from failed question",
      "difficulty": "${difficulty}",
      "hints": ["hint1", "hint2"],
      "relatedToQuestion": "Brief reference to which repair center question this addresses"
    }
  ],
  "instructions": "Review each flashcard carefully - these target your specific knowledge gaps",
  "studyTips": ["Focus on concepts you got wrong", "Review the explanations carefully", "Practice until you understand the underlying principles"]
}`;
        break;

      case 'multiple_choice':
        prompt = `
Create ${itemCount} multiple choice questions for ${certificateName} based on the specific questions the student got wrong.

${contextInfo}

IMPORTANT: Create questions that test the SAME concepts and knowledge areas as the questions above that the student failed. Each question should:
- Test the exact same underlying concepts as the failed questions
- Address the specific knowledge gaps that caused the wrong answers
- Include distractors that represent the same misconceptions the student had
- Be at ${difficulty} difficulty level
- Have explanations that clarify the concepts from the failed questions

Return as JSON:
{
  "gameType": "multiple_choice",
  "items": [
    {
      "id": "unique_id",
      "questionText": "Question testing the same concept as a failed question",
      "choiceA": "Option A",
      "choiceB": "Option B",
      "choiceC": "Option C",
      "choiceD": "Option D",
      "correctAnswer": "A",
      "explanation": "Why this answer is correct and how it relates to the failed question",
      "topic": "specific topic from failed question",
      "difficulty": "${difficulty}",
      "relatedToQuestion": "Brief reference to which repair center question this reinforces"
    }
  ],
  "instructions": "These questions target the same concepts you got wrong - choose carefully",
  "studyTips": ["Think about why you got the original questions wrong", "Focus on the underlying principles", "Review explanations thoroughly"]
}`;
        break;

      case 'true_false':
        prompt = `
Create ${itemCount} true/false statements for ${certificateName} based on the specific questions the student got wrong.

${contextInfo}

IMPORTANT: Create statements that directly test the concepts from the failed questions above. Each statement should:
- Test the exact factual knowledge or concepts from the failed questions
- Address the specific misconceptions that led to wrong answers
- Be at ${difficulty} difficulty level
- Include explanations that clarify the concepts from failed questions

Return as JSON:
{
  "gameType": "true_false",
  "items": [
    {
      "id": "unique_id",
      "statement": "Statement testing concept from a failed question",
      "isTrue": true,
      "explanation": "Why this statement is true/false and how it relates to the failed question",
      "topic": "specific topic from failed question",
      "difficulty": "${difficulty}",
      "commonMisconception": "The specific misconception from the failed question this addresses",
      "relatedToQuestion": "Brief reference to which repair center question this reinforces"
    }
  ],
  "instructions": "These statements test concepts you got wrong - think carefully",
  "studyTips": ["Remember why you got the original questions wrong", "Focus on the key facts and principles", "Pay attention to the explanations"]
}`;
        break;

      case 'fill_blank':
        prompt = `
Create ${itemCount} fill-in-the-blank exercises for ${certificateName} focusing on: ${topics.join(', ')}

These exercises should help reinforce key terminology and concepts.${contextInfo}

Each exercise should:
- Test important terminology or key facts
- Have clear context clues
- Be at ${difficulty} difficulty level
- Include multiple choice options for the blanks

Return as JSON:
{
  "gameType": "fill_blank",
  "items": [
    {
      "id": "unique_id",
      "sentence": "The sentence with _____ blanks to fill",
      "blanks": ["correct answer 1", "correct answer 2"],
      "options": ["option1", "option2", "option3", "option4"],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "Why these answers are correct"
    }
  ],
  "instructions": "Fill in the blanks with the most appropriate terms",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'matching':
        prompt = `
Create ${Math.ceil(itemCount/5)} matching exercises for ${certificateName} focusing on: ${topics.join(', ')}

Each exercise should have 5-8 items to match. These should help connect related concepts.${contextInfo}

Each exercise should:
- Connect related terms, concepts, or examples
- Help understand relationships between ideas
- Be at ${difficulty} difficulty level
- Cover important associations

Return as JSON:
{
  "gameType": "matching",
  "items": [
    {
      "id": "unique_id",
      "leftItems": ["Term 1", "Term 2", "Term 3", "Term 4", "Term 5"],
      "rightItems": ["Definition 1", "Definition 2", "Definition 3", "Definition 4", "Definition 5"],
      "correctMatches": [
        {"left": 0, "right": 0},
        {"left": 1, "right": 1}
      ],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "Why these matches are correct"
    }
  ],
  "instructions": "Match each item on the left with its corresponding item on the right",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      case 'concept_mapping':
        prompt = `
Create ${Math.ceil(itemCount/3)} concept mapping exercises for ${certificateName} focusing on: ${topics.join(', ')}

These should help students understand relationships between concepts.${contextInfo}

Each exercise should:
- Show how concepts relate to each other
- Help understand hierarchies and connections
- Be at ${difficulty} difficulty level
- Include clear relationship descriptions

Return as JSON:
{
  "gameType": "concept_mapping",
  "items": [
    {
      "id": "unique_id",
      "centralConcept": "Main concept",
      "relatedConcepts": ["concept1", "concept2", "concept3"],
      "relationships": [
        {"from": "concept1", "to": "centralConcept", "relationship": "is a type of"},
        {"from": "concept2", "to": "centralConcept", "relationship": "leads to"}
      ],
      "topic": "specific topic",
      "difficulty": "${difficulty}",
      "explanation": "How these concepts relate"
    }
  ],
  "instructions": "Map the relationships between concepts",
  "studyTips": ["tip1", "tip2", "tip3"]
}`;
        break;

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid game type' },
          { status: 400 }
        );
    }

    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    try {
      // Try to parse the JSON response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('No JSON found in response');
      }

      const gameData = JSON.parse(jsonMatch[0]);
      
      return NextResponse.json({
        success: true,
        data: gameData
      });

    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      
      // Fallback response based on game type
      const fallbackData = {
        gameType,
        items: [],
        instructions: `Practice with these ${gameType} to improve your understanding.`,
        studyTips: [
          "Review each item carefully",
          "Focus on understanding rather than memorization",
          "Practice regularly for best results"
        ]
      };

      return NextResponse.json({
        success: true,
        data: fallbackData
      });
    }

  } catch (error) {
    console.error('Error in GenerateRepairCenterExam API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to generate repair center exam',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

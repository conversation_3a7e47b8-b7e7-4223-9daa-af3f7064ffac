import React from "react";
import { Locale } from "@/i18n-config";
import { Toaster } from "@/components/ui/toaster";
import LanguageSwitcher from "@/components/ui/language-switcher";
import { GraduationCap } from "lucide-react";

export default async function AuthLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}) {
  // Extract lang parameter but don't use it in this layout
  await params;

  return (
    <div className="min-h-screen">
      {/* Language Switcher */}
      <div className="absolute top-4 right-4 z-20">
        <LanguageSwitcher />
      </div>

      {/* Header with Logo */}
      <header className="absolute top-4 left-4 z-20">
        <div className="flex items-center space-x-2">
          <GraduationCap className="h-8 w-8 text-blue-600" />
          <span className="text-xl font-bold text-gray-900 dark:text-white">
            Certification Portal
          </span>
        </div>
      </header>

      {/* Main content */}
      <main className="min-h-screen">
        {children}
      </main>

      {/* Toast notifications */}
      <Toaster />
    </div>
  );
}

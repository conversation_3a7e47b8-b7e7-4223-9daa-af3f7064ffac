import { firestore } from "../firestoreConfig"; // Corrected import
import { doc, setDoc, getDoc, updateDoc, collection, CollectionReference } from "firebase/firestore";

// Define roles using an enum for type safety and easy customization
// Add or remove roles as needed for your application
export enum UserRole {
  VIEWER = 'viewer',
  CUSTOMER = 'customer', // Example role
  // Add more roles here
}

// Define the structure for user data in Firestore
export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL?: string | null;
  role: UserRole; // Assign a default role or handle it during creation
  createdAt: Date;
  // Add any other user-specific fields here
}

const usersCollection = collection(firestore, "users") as CollectionReference<UserProfile>;

/**
 * Creates or updates a user profile in Firestore.
 * Usually called after user creation or sign-in.
 *
 * @param uid - The user's unique ID from Firebase Authentication.
 * @param data - Partial user data to create or update. Email is typically required.
 */
export const upsertUserProfile = async (uid: string, data: Partial<Omit<UserProfile, 'uid' | 'createdAt' | 'role'>> & { email: string }): Promise<void> => {
  const userRef = doc(usersCollection, uid);
  const docSnap = await getDoc(userRef);

  if (docSnap.exists()) {
    // Update existing user - avoid overwriting role or createdAt
    const existingData = docSnap.data();
    await updateDoc(userRef, {
      ...data,
      displayName: data.displayName ?? existingData?.displayName ?? null,
      photoURL: data.photoURL ?? existingData?.photoURL ?? null,
      // Ensure email updates if provided
      email: data.email ?? existingData?.email,
    });
    console.log("User profile updated:", uid);
  } else {
    // Create new user - set default role and createdAt
    const newUserProfile: UserProfile = {
      uid: uid,
      email: data.email,
      displayName: data.displayName ?? null,
      photoURL: data.photoURL ?? null,
      role: UserRole.CUSTOMER, // Assign a default role for new users
      createdAt: new Date(),
    };
    await setDoc(userRef, newUserProfile);
    console.log("User profile created:", uid);
  }
};

/**
 * Retrieves a user's profile from Firestore.
 *
 * @param uid - The user's unique ID.
 * @returns The user profile data or null if not found.
 */
export const getUserProfile = async (uid: string): Promise<UserProfile | null> => {
  const userRef = doc(usersCollection, uid);
  const docSnap = await getDoc(userRef);

  if (docSnap.exists()) {
    // Explicitly cast to UserProfile, assuming data structure matches
    return docSnap.data() as UserProfile;
  } else {
    console.log("No such user profile found!");
    return null;
  }
};

/**
 * Updates a user's role in Firestore.
 * Ensure proper authorization checks before calling this function.
 *
 * @param uid - The user's unique ID.
 * @param newRole - The new role to assign.
 */
export const updateUserRole = async (uid: string, newRole: UserRole): Promise<void> => {
  if (!Object.values(UserRole).includes(newRole)) {
      throw new Error(`Invalid role: ${newRole}. Must be one of ${Object.values(UserRole).join(', ')}`);
  }
  const userRef = doc(usersCollection, uid);
   // Check if user exists before updating
   const docSnap = await getDoc(userRef);
   if (!docSnap.exists()) {
       throw new Error(`User with UID ${uid} not found.`);
   }
  await updateDoc(userRef, { role: newRole });
  console.log(`User role updated for ${uid} to ${newRole}`);
};

// Example usage (add more functions as needed, e.g., deleteUser, listUsers)
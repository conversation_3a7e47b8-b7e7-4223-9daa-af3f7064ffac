"use client";

import React, { useState, useEffect, useCallback } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import { getCertificateQuestions, Question } from "@/Firebase/firestore/services/QuestionsService";
import {
  createGameAttempt,
  addGameQuestionResult,
  completeGameAttempt
} from "@/Firebase/firestore/services/GameService";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import {
  ArrowLeft,
  Trophy,
  Target,
  Zap,
  Clock,
  Star,
  Crown,
  Loader2,
  Play,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Lightbulb
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";

interface PrivacyStakeGameProps {
  certificate: Certificate;
  onBack: () => void;
}

interface Player {
  name: string;
  score: number;
  isActive: boolean;
}

interface ChoiceExplanation {
  choice: 'A' | 'B' | 'C' | 'D';
  explanation: string;
  isCorrect: boolean;
  reasoning: string;
}

interface QuestionExplanationData {
  explanation: string;
  choiceExplanations: ChoiceExplanation[];
  keyPoints: string[];
  relatedConcepts: string[];
  difficulty: 'Easy' | 'Medium' | 'Hard';
  studyTips: string[];
}

interface GameState {
  phase: 'setup' | 'wager' | 'answer' | 'steal' | 'result' | 'gameOver';
  currentRound: number;
  totalRounds: number;
  currentQuestion: Question | null;
  players: [Player, Player];
  wagers: [number | null, number | null];
  answers: [string | null, string | null];
  highBidder: 0 | 1 | null;
  correctAnswer: string | null;
  roundWinner: 0 | 1 | null;
  gameWinner: 0 | 1 | null;
  questionExplanation: QuestionExplanationData | null;
  isLoadingExplanation: boolean;
  timeRemaining: number;
  highBidderAnswer: string | null;
  gameAttemptId: string | null;
  roundStartTime: number | null;
  answerStartTime: number | null;
  stealStartTime: number | null;
}

export default function PrivacyStakeGame({ certificate, onBack }: PrivacyStakeGameProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  
  const [questions, setQuestions] = useState<Question[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [gameState, setGameState] = useState<GameState>({
    phase: 'setup',
    currentRound: 0,
    totalRounds: 15,
    currentQuestion: null,
    players: [
      { name: '', score: 1000, isActive: false },
      { name: '', score: 1000, isActive: false }
    ],
    wagers: [null, null],
    answers: [null, null],
    highBidder: null,
    correctAnswer: null,
    roundWinner: null,
    gameWinner: null,
    questionExplanation: null,
    isLoadingExplanation: false,
    timeRemaining: 60,
    highBidderAnswer: null,
    gameAttemptId: null,
    roundStartTime: null,
    answerStartTime: null,
    stealStartTime: null
  });

  const [playerInputs, setPlayerInputs] = useState({
    player1Name: '',
    player2Name: ''
  });

  const [currentWager, setCurrentWager] = useState<string>('');
  const [currentAnswer, setCurrentAnswer] = useState<string>('');
  const [showAdditionalLearning, setShowAdditionalLearning] = useState<boolean>(false);

  const loadQuestions = useCallback(async () => {
    if (!user?.uid || !certificate.id) return;

    try {
      setIsLoading(true);
      const fetchedQuestions = await getCertificateQuestions(user.uid, certificate.id);

      if (fetchedQuestions.length < 15) {
        toast({
          title: "Insufficient Questions",
          description: `You need at least 15 questions to play. You currently have ${fetchedQuestions.length} questions.`,
          variant: "destructive",
        });
        return;
      }

      // Shuffle questions for random selection
      const shuffled = [...fetchedQuestions].sort(() => Math.random() - 0.5);
      setQuestions(shuffled);
    } catch (error) {
      console.error('Error loading questions:', error);
      toast({
        title: "Error",
        description: "Failed to load questions for the game.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, certificate.id, toast]);

  useEffect(() => {
    loadQuestions();
  }, [loadQuestions]);

  const fetchQuestionExplanation = useCallback(async (question: Question) => {
    try {
      setGameState(prev => ({ ...prev, isLoadingExplanation: true }));

      const response = await fetch('/api/QuestionExplanation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          question: question.question,
          choiceA: question.choiceA,
          choiceB: question.choiceB,
          choiceC: question.choiceC,
          choiceD: question.choiceD,
          topic: question.category || 'GDPR',
          certificateName: certificate.name
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch explanation');
      }

      const result = await response.json();

      if (result.success && result.data) {
        setGameState(prev => ({
          ...prev,
          questionExplanation: result.data,
          isLoadingExplanation: false
        }));
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('Error fetching question explanation:', error);
      setGameState(prev => ({ ...prev, isLoadingExplanation: false }));
      toast({
        title: "Explanation Error",
        description: "Could not load detailed explanation. Basic explanation will be shown.",
        variant: "destructive",
      });
    }
  }, [certificate.name, toast]);

  const handleTimeoutAnswer = useCallback(async () => {
    // High bidder loses their wager due to timeout
    const highBidderIndex = gameState.highBidder!;
    const wager = gameState.wagers[highBidderIndex]!;
    const newPlayers = [...gameState.players];
    newPlayers[highBidderIndex].score -= wager;

    const otherPlayerIndex = highBidderIndex === 0 ? 1 : 0;

    setGameState(prev => ({
      ...prev,
      phase: 'steal',
      correctAnswer: prev.currentQuestion?.correctAnswer || null,
      timeRemaining: 30, // 30 seconds for steal
      highBidderAnswer: null, // No answer was given
      stealStartTime: Date.now(),
      players: newPlayers.map((p, i) => ({ ...p, isActive: i === otherPlayerIndex })) as [Player, Player] as [Player, Player]
    }));

    toast({
      title: "Time's Up!",
      description: `${gameState.players[highBidderIndex].name} ran out of time and lost ${wager} points.`,
      variant: "destructive",
    });

    // Additional notification for repair center
    toast({
      title: "Added to Repair Center",
      description: "This timed-out question has been added to your repair center for review.",
      variant: "default",
    });
  }, [gameState.highBidder, gameState.wagers, gameState.players, toast]);

  const handleTimeoutSteal = useCallback(async () => {
    const timeSpentSteal = gameState.stealStartTime ? Math.floor((Date.now() - gameState.stealStartTime) / 1000) : 30;
    const timeSpentAnswer = gameState.answerStartTime ? Math.floor((gameState.stealStartTime! - gameState.answerStartTime) / 1000) : 0;

    // Track the complete question result including failed steal attempt
    if (gameState.gameAttemptId && gameState.currentQuestion) {
      try {
        await addGameQuestionResult(gameState.gameAttemptId, {
          questionId: gameState.currentQuestion.id!,
          questionText: gameState.currentQuestion.question,
          choiceA: gameState.currentQuestion.choiceA,
          choiceB: gameState.currentQuestion.choiceB,
          choiceC: gameState.currentQuestion.choiceC,
          choiceD: gameState.currentQuestion.choiceD,
          correctAnswer: gameState.currentQuestion.correctAnswer,
          roundNumber: gameState.currentRound,
          highBidder: gameState.highBidder!,
          highBidderWager: gameState.wagers[gameState.highBidder!]!,
          highBidderAnswer: (gameState.highBidderAnswer as 'A' | 'B' | 'C' | 'D') || undefined,
          highBidderCorrect: false,
          stealerAnswer: undefined, // No steal answer due to timeout
          stealerCorrect: false,
          roundWinner: null,
          timeSpentAnswer,
          timeSpentSteal,
          topic: gameState.currentQuestion.category || undefined,
          explanation: gameState.currentQuestion.explanation || undefined
        });
      } catch (error) {
        console.error('Error tracking question result:', error);
      }
    }

    // No penalty for failed steal due to timeout
    setGameState(prev => ({
      ...prev,
      phase: 'result',
      roundWinner: null
    }));

    // Fetch detailed explanation for the result phase
    if (gameState.currentQuestion) {
      await fetchQuestionExplanation(gameState.currentQuestion);
    }

    const stealerIndex = gameState.players.findIndex(p => p.isActive);
    toast({
      title: "Time's Up!",
      description: `${gameState.players[stealerIndex].name} ran out of time for the steal attempt.`,
    });

    // Additional notification for repair center (timeout on steal attempt)
    toast({
      title: "Added to Repair Center",
      description: "This timed-out question has been added to your repair center for review.",
      variant: "default",
    });
  }, [gameState.gameAttemptId, gameState.currentQuestion, gameState.stealStartTime, gameState.answerStartTime, gameState.currentRound, gameState.highBidder, gameState.wagers, gameState.highBidderAnswer, gameState.players, toast, fetchQuestionExplanation]);

  // Timer effect
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if ((gameState.phase === 'answer' || gameState.phase === 'steal') && gameState.timeRemaining > 0) {
      interval = setInterval(() => {
        setGameState(prev => ({
          ...prev,
          timeRemaining: prev.timeRemaining - 1
        }));
      }, 1000);
    }

    // Auto-submit when time runs out
    if (gameState.timeRemaining === 0) {
      if (gameState.phase === 'answer') {
        handleTimeoutAnswer();
      } else if (gameState.phase === 'steal') {
        handleTimeoutSteal();
      }
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [gameState.phase, gameState.timeRemaining, handleTimeoutAnswer, handleTimeoutSteal]);

  const startGame = async () => {
    if (!playerInputs.player1Name.trim() || !playerInputs.player2Name.trim()) {
      toast({
        title: "Player Names Required",
        description: "Please enter names for both players.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Create game attempt in Firebase
      const gameAttemptId = await createGameAttempt(
        user!.uid,
        certificate.id!,
        'privacy-stake',
        playerInputs.player1Name.trim(),
        playerInputs.player2Name.trim(),
        15
      );

      setGameState(prev => ({
        ...prev,
        phase: 'wager',
        currentRound: 1,
        currentQuestion: questions[0],
        gameAttemptId,
        roundStartTime: Date.now(),
        players: [
          { name: playerInputs.player1Name.trim(), score: 1000, isActive: true },
          { name: playerInputs.player2Name.trim(), score: 1000, isActive: false }
        ]
      }));
    } catch (error) {
      console.error('Error starting game:', error);
      toast({
        title: "Error",
        description: "Failed to start game. Please try again.",
        variant: "destructive",
      });
    }
  };

  const submitWager = () => {
    const wager = parseInt(currentWager);
    const activePlayerIndex = gameState.players.findIndex(p => p.isActive);

    if (isNaN(wager) || wager <= 0 || wager > gameState.players[activePlayerIndex].score) {
      toast({
        title: "Invalid Wager",
        description: `Please enter a valid wager between 1 and ${gameState.players[activePlayerIndex].score}.`,
        variant: "destructive",
      });
      return;
    }

    const newWagers = [...gameState.wagers] as [number | null, number | null];
    newWagers[activePlayerIndex] = wager;

    // Switch to next player or move to answer phase
    if (newWagers[0] !== null && newWagers[1] !== null) {
      // Both players have wagered, determine high bidder
      const highBidder = newWagers[0]! > newWagers[1]! ? 0 : 1;

      setGameState(prev => ({
        ...prev,
        phase: 'answer',
        wagers: newWagers,
        highBidder,
        timeRemaining: 60, // 1 minute for answer
        answerStartTime: Date.now(),
        players: prev.players.map((p, i) => ({ ...p, isActive: i === highBidder })) as [Player, Player]
      }));
    } else {
      // Switch to other player
      const nextActiveIndex = activePlayerIndex === 0 ? 1 : 0;
      setGameState(prev => ({
        ...prev,
        wagers: newWagers,
        players: prev.players.map((p, i) => ({ ...p, isActive: i === nextActiveIndex })) as [Player, Player]
      }));
    }

    setCurrentWager('');
  };

  const submitAnswer = async () => {
    if (!currentAnswer) {
      toast({
        title: "Answer Required",
        description: "Please select an answer.",
        variant: "destructive",
      });
      return;
    }

    const highBidderIndex = gameState.highBidder!;
    const isCorrect = currentAnswer === gameState.currentQuestion?.correctAnswer;
    const wager = gameState.wagers[highBidderIndex]!;
    const timeSpentAnswer = gameState.answerStartTime ? Math.floor((Date.now() - gameState.answerStartTime) / 1000) : 0;

    const newPlayers = [...gameState.players];

    if (isCorrect) {
      // High bidder wins their wager
      newPlayers[highBidderIndex].score += wager;

      // Track the question result
      if (gameState.gameAttemptId && gameState.currentQuestion) {
        try {
          await addGameQuestionResult(gameState.gameAttemptId, {
            questionId: gameState.currentQuestion.id!,
            questionText: gameState.currentQuestion.question,
            choiceA: gameState.currentQuestion.choiceA,
            choiceB: gameState.currentQuestion.choiceB,
            choiceC: gameState.currentQuestion.choiceC,
            choiceD: gameState.currentQuestion.choiceD,
            correctAnswer: gameState.currentQuestion.correctAnswer,
            roundNumber: gameState.currentRound,
            highBidder: highBidderIndex,
            highBidderWager: wager,
            highBidderAnswer: currentAnswer,
            highBidderCorrect: true,
            roundWinner: highBidderIndex,
            timeSpentAnswer,
            topic: gameState.currentQuestion.category || undefined,
            explanation: gameState.currentQuestion.explanation || undefined
          });
        } catch (error) {
          console.error('Error tracking question result:', error);
        }
      }

      setGameState(prev => ({
        ...prev,
        phase: 'result',
        correctAnswer: prev.currentQuestion?.correctAnswer || null,
        roundWinner: highBidderIndex,
        players: newPlayers as [Player, Player]
      }));

      // Fetch detailed explanation for the result phase
      if (gameState.currentQuestion) {
        await fetchQuestionExplanation(gameState.currentQuestion);
      }
    } else {
      // High bidder loses their wager, opponent gets steal chance
      newPlayers[highBidderIndex].score -= wager;
      const otherPlayerIndex = highBidderIndex === 0 ? 1 : 0;

      // Notify that question was added to repair center
      toast({
        title: "Added to Repair Center",
        description: "This incorrect answer has been added to your repair center for review.",
        variant: "default",
      });

      setGameState(prev => ({
        ...prev,
        phase: 'steal',
        correctAnswer: prev.currentQuestion?.correctAnswer || null,
        timeRemaining: 30, // 30 seconds for steal
        highBidderAnswer: currentAnswer as 'A' | 'B' | 'C' | 'D', // Store the wrong answer
        stealStartTime: Date.now(),
        players: newPlayers.map((p, i) => ({ ...p, isActive: i === otherPlayerIndex })) as [Player, Player]
      }));
    }

    setCurrentAnswer('');
  };

  const submitSteal = async () => {
    if (!currentAnswer) {
      toast({
        title: "Answer Required",
        description: "Please select an answer.",
        variant: "destructive",
      });
      return;
    }

    const stealerIndex = gameState.players.findIndex(p => p.isActive);
    const isCorrect = currentAnswer === gameState.currentQuestion?.correctAnswer;
    const timeSpentSteal = gameState.stealStartTime ? Math.floor((Date.now() - gameState.stealStartTime) / 1000) : 0;
    const timeSpentAnswer = gameState.answerStartTime ? Math.floor((gameState.stealStartTime! - gameState.answerStartTime) / 1000) : 0;
    const wager = gameState.wagers[gameState.highBidder!]!; // Get the original wager amount

    const newPlayers = [...gameState.players];
    let roundWinner = null;

    if (isCorrect) {
      // Stealer gets the full wager amount
      newPlayers[stealerIndex].score += wager;
      roundWinner = stealerIndex;
    } else {
      // Notify that question was added to repair center for incorrect steal
      toast({
        title: "Added to Repair Center",
        description: "This incorrect steal attempt has been added to your repair center for review.",
        variant: "default",
      });
    }

    // Track the complete question result including steal attempt
    if (gameState.gameAttemptId && gameState.currentQuestion) {
      try {
        await addGameQuestionResult(gameState.gameAttemptId, {
          questionId: gameState.currentQuestion.id!,
          questionText: gameState.currentQuestion.question,
          choiceA: gameState.currentQuestion.choiceA,
          choiceB: gameState.currentQuestion.choiceB,
          choiceC: gameState.currentQuestion.choiceC,
          choiceD: gameState.currentQuestion.choiceD,
          correctAnswer: gameState.currentQuestion.correctAnswer,
          roundNumber: gameState.currentRound,
          highBidder: gameState.highBidder!,
          highBidderWager: wager,
          highBidderAnswer: gameState.highBidderAnswer! as 'A' | 'B' | 'C' | 'D',
          highBidderCorrect: false,
          stealerAnswer: currentAnswer as 'A' | 'B' | 'C' | 'D',
          stealerCorrect: isCorrect,
          roundWinner: roundWinner as 0 | 1 | null,
          timeSpentAnswer,
          timeSpentSteal,
          topic: gameState.currentQuestion.category || undefined,
          explanation: gameState.currentQuestion.explanation || undefined
        });
      } catch (error) {
        console.error('Error tracking question result:', error);
      }
    }

    setGameState(prev => ({
      ...prev,
      phase: 'result',
      roundWinner: roundWinner as 0 | 1 | null,
      players: newPlayers as [Player, Player]
    }));

    // Fetch detailed explanation for the result phase
    if (gameState.currentQuestion) {
      await fetchQuestionExplanation(gameState.currentQuestion);
    }

    setCurrentAnswer('');
  };

  const nextRound = async () => {
    if (gameState.currentRound >= gameState.totalRounds) {
      // Game over - complete the game attempt
      const winner = gameState.players[0].score > gameState.players[1].score ? 0 :
                    gameState.players[1].score > gameState.players[0].score ? 1 : null;

      if (gameState.gameAttemptId) {
        try {
          await completeGameAttempt(
            gameState.gameAttemptId,
            gameState.players[0].score,
            gameState.players[1].score,
            gameState.currentRound
          );

          toast({
            title: "Game Completed!",
            description: "Your game results have been saved and incorrect answers added to the repair center.",
          });
        } catch (error) {
          console.error('Error completing game:', error);
          toast({
            title: "Warning",
            description: "Game completed but results may not have been saved properly.",
            variant: "destructive",
          });
        }
      }

      setGameState(prev => ({
        ...prev,
        phase: 'gameOver',
        gameWinner: winner
      }));
    } else {
      // Next round
      const nextRoundIndex = gameState.currentRound;
      setGameState(prev => ({
        ...prev,
        phase: 'wager',
        currentRound: prev.currentRound + 1,
        currentQuestion: questions[nextRoundIndex],
        wagers: [null, null],
        answers: [null, null],
        highBidder: null,
        correctAnswer: null,
        roundWinner: null,
        questionExplanation: null,
        isLoadingExplanation: false,
        timeRemaining: 60,
        highBidderAnswer: null,
        roundStartTime: Date.now(),
        answerStartTime: null,
        stealStartTime: null,
        players: prev.players.map((p, i) => ({ ...p, isActive: i === 0 })) as [Player, Player]
      }));
      setShowAdditionalLearning(false);
    }
  };

  const resetGame = () => {
    setGameState({
      phase: 'setup',
      currentRound: 0,
      totalRounds: 15,
      currentQuestion: null,
      players: [
        { name: '', score: 1000, isActive: false },
        { name: '', score: 1000, isActive: false }
      ],
      wagers: [null, null],
      answers: [null, null],
      highBidder: null,
      correctAnswer: null,
      roundWinner: null,
      gameWinner: null,
      questionExplanation: null,
      isLoadingExplanation: false,
      timeRemaining: 60,
      highBidderAnswer: null,
      gameAttemptId: null,
      roundStartTime: null,
      answerStartTime: null,
      stealStartTime: null
    });
    setPlayerInputs({ player1Name: '', player2Name: '' });
    setShowAdditionalLearning(false);
  };

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-500 dark:text-gray-400">Loading game questions...</p>
        </div>
      </div>
    );
  }

  if (questions.length < 15) {
    return (
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="text-center">
          <Target className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            Insufficient Questions
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            You need at least 15 questions to play Privacy Stake. You currently have {questions.length} questions.
          </p>
          <Button onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <style jsx>{`
        input[type="range"]::-webkit-slider-thumb {
          appearance: none;
          height: 20px;
          width: 20px;
          border-radius: 4px;
          background: linear-gradient(135deg, #2563eb, #1d4ed8);
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        input[type="range"]::-moz-range-thumb {
          height: 20px;
          width: 20px;
          border-radius: 4px;
          background: linear-gradient(135deg, #2563eb, #1d4ed8);
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
      `}</style>
      <div className="max-w-7xl mx-auto px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <Button variant="ghost" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Games
          </Button>
          <div className="flex items-center gap-3">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-3 rounded-xl shadow-lg">
              <Target className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Privacy Stake
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Strategic 1v1 Quiz Battle
              </p>
            </div>
          </div>
        </div>
        
        {gameState.phase !== 'setup' && (
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="text-sm">
              Round {gameState.currentRound} / {gameState.totalRounds}
            </Badge>
          </div>
        )}
      </div>

      {/* Game Content */}
      {gameState.phase === 'setup' && (
        <Card className="max-w-2xl mx-auto">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Game Setup
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              Enter player names to begin the Privacy Stake challenge
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="player1">Player 1 Name</Label>
                <Input
                  id="player1"
                  placeholder="Enter player 1 name"
                  value={playerInputs.player1Name}
                  onChange={(e) => setPlayerInputs(prev => ({ ...prev, player1Name: e.target.value }))}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="player2">Player 2 Name</Label>
                <Input
                  id="player2"
                  placeholder="Enter player 2 name"
                  value={playerInputs.player2Name}
                  onChange={(e) => setPlayerInputs(prev => ({ ...prev, player2Name: e.target.value }))}
                />
              </div>
            </div>
            
            <div className="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
              <h3 className="font-semibold text-slate-900 dark:text-slate-100 mb-3">Competition Framework:</h3>
              <ul className="text-sm text-slate-700 dark:text-slate-300 space-y-2">
                <li>• Initial capital allocation: 1,000 points per participant</li>
                <li>• Investment strategy: Confidence-based point allocation per question</li>
                <li>• Market advantage: Highest investor gains first-mover advantage</li>
                <li>• Time constraints: 60 seconds for primary decisions, 30 seconds for opportunity capture</li>
                <li>• Risk management: Timeout results in automatic loss of invested capital</li>
                <li>• Return structure: Correct decisions yield 1:1 returns, incorrect decisions result in total loss</li>
                <li>• Opportunity arbitrage: Failed primary decisions allow opponents to capture the full wagered amount</li>
                <li>• Information transparency: Previous decisions are disclosed during arbitrage phase</li>
                <li>• Competition duration: 15 rounds with highest capital accumulation determining winner</li>
              </ul>
            </div>
            
            <Button onClick={startGame} className="w-full" size="lg">
              <Play className="h-4 w-4 mr-2" />
              Start Game
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Wager Phase */}
      {gameState.phase === 'wager' && (
        <div className="space-y-6">
          {/* Player Scores */}
          <div className="grid grid-cols-2 gap-4">
            {gameState.players.map((player, index) => (
              <Card key={index} className={`${player.isActive ? 'ring-2 ring-blue-500' : ''}`}>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Crown className={`h-5 w-5 ${player.isActive ? 'text-yellow-500' : 'text-gray-400'}`} />
                    <h3 className="font-bold text-lg">{player.name}</h3>
                  </div>
                  <div className="text-2xl font-bold text-blue-600">{player.score}</div>
                  <div className="text-sm text-gray-500">points</div>
                  {gameState.wagers[index] !== null && (
                    <Badge className="mt-2">Wagered: {gameState.wagers[index]}</Badge>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Current Question (Question Only) */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Question {gameState.currentRound}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>
              <p className="text-center text-sm text-gray-500 mt-4">
                Answer choices will be revealed after both players wager
              </p>
            </CardContent>
          </Card>

          {/* Professional Wager Interface */}
          <Card className="border border-slate-300 dark:border-slate-600 bg-white dark:bg-slate-800 shadow-xl">
            <CardHeader className="border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-900/50">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2.5 rounded-lg shadow-md">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-semibold text-slate-900 dark:text-slate-100">
                      Confidence Investment
                    </CardTitle>
                    <p className="text-slate-600 dark:text-slate-400 text-sm">
                      {gameState.players.find(p => p.isActive)?.name} • Round {gameState.currentRound}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm text-slate-500 dark:text-slate-400">Available Capital</div>
                  <div className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                    {gameState.players.find(p => p.isActive)?.score.toLocaleString()}
                  </div>
                  <div className="text-xs text-slate-500">points</div>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-6 p-6">
              {/* Investment Strategy Presets */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-semibold text-slate-700 dark:text-slate-300">
                    Investment Strategy
                  </Label>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    Select confidence level
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {[
                    {
                      label: "Conservative",
                      percentage: 10,
                      description: "Low risk, steady approach",
                      bgColor: "bg-emerald-50 dark:bg-emerald-900/20",
                      borderColor: "border-emerald-200 dark:border-emerald-700",
                      textColor: "text-emerald-700 dark:text-emerald-300"
                    },
                    {
                      label: "Moderate",
                      percentage: 25,
                      description: "Balanced risk-reward",
                      bgColor: "bg-blue-50 dark:bg-blue-900/20",
                      borderColor: "border-blue-200 dark:border-blue-700",
                      textColor: "text-blue-700 dark:text-blue-300"
                    },
                    {
                      label: "Aggressive",
                      percentage: 50,
                      description: "High confidence play",
                      bgColor: "bg-amber-50 dark:bg-amber-900/20",
                      borderColor: "border-amber-200 dark:border-amber-700",
                      textColor: "text-amber-700 dark:text-amber-300"
                    },
                    {
                      label: "Maximum",
                      percentage: 100,
                      description: "All-in commitment",
                      bgColor: "bg-red-50 dark:bg-red-900/20",
                      borderColor: "border-red-200 dark:border-red-700",
                      textColor: "text-red-700 dark:text-red-300"
                    }
                  ].map((strategy) => {
                    const activePlayer = gameState.players.find(p => p.isActive);
                    const amount = Math.floor((activePlayer?.score || 0) * (strategy.percentage / 100));
                    const isSelected = parseInt(currentWager) === amount;
                    return (
                      <Button
                        key={strategy.label}
                        variant="outline"
                        className={`h-20 flex flex-col items-center justify-center transition-all duration-200 ${strategy.bgColor} ${strategy.borderColor} ${strategy.textColor} hover:shadow-md ${
                          isSelected ? 'ring-2 ring-blue-500 shadow-lg scale-105' : 'hover:scale-102'
                        }`}
                        onClick={() => setCurrentWager(amount.toString())}
                      >
                        <span className="text-sm font-semibold">{strategy.label}</span>
                        <span className="text-xs opacity-75">{strategy.description}</span>
                        <span className="text-lg font-bold">{amount.toLocaleString()}</span>
                      </Button>
                    );
                  })}
                </div>
              </div>

              {/* Custom Investment Amount */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-base font-semibold text-slate-700 dark:text-slate-300">
                    Custom Investment
                  </Label>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    Precise amount control
                  </div>
                </div>

                <div className="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
                  <div className="relative">
                    <Input
                      id="wager"
                      type="number"
                      placeholder="Enter investment amount"
                      value={currentWager}
                      onChange={(e) => setCurrentWager(e.target.value)}
                      min="1"
                      max={gameState.players.find(p => p.isActive)?.score}
                      className="text-2xl font-bold text-center py-4 border-2 border-slate-300 dark:border-slate-600 focus:border-blue-500 bg-white dark:bg-slate-800"
                    />
                    <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-sm text-slate-500 font-medium">
                      POINTS
                    </div>
                  </div>

                  {/* Investment Slider */}
                  <div className="mt-4 space-y-2">
                    <input
                      type="range"
                      min="1"
                      max={gameState.players.find(p => p.isActive)?.score || 1000}
                      value={currentWager || 0}
                      onChange={(e) => setCurrentWager(e.target.value)}
                      className="w-full h-2 bg-slate-200 dark:bg-slate-700 rounded-lg appearance-none cursor-pointer"
                    />
                    <div className="flex justify-between text-xs text-slate-500">
                      <span>1</span>
                      <span className="font-semibold text-slate-700 dark:text-slate-300">
                        {currentWager ? `${Math.round((parseInt(currentWager) / (gameState.players.find(p => p.isActive)?.score || 1)) * 100)}% allocation` : 'Select amount'}
                      </span>
                      <span>{gameState.players.find(p => p.isActive)?.score?.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Investment Analysis */}
              {currentWager && (
                <div className="bg-slate-50 dark:bg-slate-900/50 rounded-lg p-4 border border-slate-200 dark:border-slate-700">
                  <div className="flex items-center justify-between mb-3">
                    <span className="font-semibold text-slate-700 dark:text-slate-300">Risk Assessment</span>
                    <Badge className={`${
                      parseInt(currentWager) <= (gameState.players.find(p => p.isActive)?.score || 0) * 0.2 ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300' :
                      parseInt(currentWager) <= (gameState.players.find(p => p.isActive)?.score || 0) * 0.5 ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300' :
                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    }`}>
                      {parseInt(currentWager) <= (gameState.players.find(p => p.isActive)?.score || 0) * 0.2 ? 'Low Risk' :
                       parseInt(currentWager) <= (gameState.players.find(p => p.isActive)?.score || 0) * 0.5 ? 'Medium Risk' :
                       'High Risk'}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className="text-emerald-600 dark:text-emerald-400 font-bold text-lg">+{parseInt(currentWager).toLocaleString()}</div>
                      <div className="text-slate-500 dark:text-slate-400">Potential Gain</div>
                    </div>
                    <div className="text-center">
                      <div className="text-red-600 dark:text-red-400 font-bold text-lg">-{parseInt(currentWager).toLocaleString()}</div>
                      <div className="text-slate-500 dark:text-slate-400">Potential Loss</div>
                    </div>
                    <div className="text-center">
                      <div className="text-slate-700 dark:text-slate-300 font-bold text-lg">{((gameState.players.find(p => p.isActive)?.score || 0) - parseInt(currentWager)).toLocaleString()}</div>
                      <div className="text-slate-500 dark:text-slate-400">Remaining Capital</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Execute Investment Button */}
              <Button
                onClick={submitWager}
                className="w-full h-12 text-lg font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                disabled={!currentWager || parseInt(currentWager) <= 0 || parseInt(currentWager) > (gameState.players.find(p => p.isActive)?.score || 0)}
              >
                <Target className="h-5 w-5 mr-2" />
                Execute Investment: {currentWager ? parseInt(currentWager).toLocaleString() : 0} Points
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Answer Phase */}
      {gameState.phase === 'answer' && (
        <div className="space-y-6">
          {/* Player Scores */}
          <div className="grid grid-cols-2 gap-4">
            {gameState.players.map((player, index) => (
              <Card key={index} className={`${player.isActive ? 'ring-2 ring-blue-500' : ''}`}>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Crown className={`h-5 w-5 ${player.isActive ? 'text-yellow-500' : 'text-gray-400'}`} />
                    <h3 className="font-bold text-lg">{player.name}</h3>
                    {index === gameState.highBidder && (
                      <Badge className="bg-yellow-500">High Bidder</Badge>
                    )}
                  </div>
                  <div className="text-2xl font-bold text-blue-600">{player.score}</div>
                  <div className="text-sm text-gray-500">points</div>
                  <Badge className="mt-2">Wagered: {gameState.wagers[index]}</Badge>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Timer Display */}
          <Card className={`border-2 ${gameState.timeRemaining <= 10 ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'}`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-center gap-3">
                <Clock className={`h-6 w-6 ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-blue-600'}`} />
                <div className="text-center">
                  <div className={`text-3xl font-bold ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-blue-600'}`}>
                    {Math.floor(gameState.timeRemaining / 60)}:{(gameState.timeRemaining % 60).toString().padStart(2, '0')}
                  </div>
                  <div className={`text-sm ${gameState.timeRemaining <= 10 ? 'text-red-500' : 'text-blue-500'}`}>
                    Time Remaining
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Question with Choices */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Question {gameState.currentRound}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>

              <div className="grid grid-cols-1 gap-3">
                {['A', 'B', 'C', 'D'].map((choice) => (
                  <Button
                    key={choice}
                    variant={currentAnswer === choice ? "default" : "outline"}
                    className="text-left justify-start p-4 h-auto"
                    onClick={() => setCurrentAnswer(choice)}
                  >
                    <span className="font-bold mr-3">{choice}.</span>
                    <span>{gameState.currentQuestion?.[`choice${choice}` as keyof Question] as string}</span>
                  </Button>
                ))}
              </div>

              <Button
                onClick={submitAnswer}
                className={`w-full ${gameState.timeRemaining <= 10 ? 'bg-red-600 hover:bg-red-700 animate-pulse' : ''}`}
                disabled={!currentAnswer}
              >
                <Target className="h-4 w-4 mr-2" />
                {gameState.timeRemaining <= 10 ? 'Submit Now!' : 'Submit Answer'}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Steal Phase */}
      {gameState.phase === 'steal' && (
        <div className="space-y-6">
          {/* Player Scores */}
          <div className="grid grid-cols-2 gap-4">
            {gameState.players.map((player, index) => (
              <Card key={index} className={`${player.isActive ? 'ring-2 ring-green-500' : ''}`}>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Star className={`h-5 w-5 ${player.isActive ? 'text-green-500' : 'text-gray-400'}`} />
                    <h3 className="font-bold text-lg">{player.name}</h3>
                    {player.isActive && (
                      <Badge className="bg-green-500">Steal Chance</Badge>
                    )}
                  </div>
                  <div className="text-2xl font-bold text-blue-600">{player.score}</div>
                  <div className="text-sm text-gray-500">points</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Timer Display for Steal */}
          <Card className={`border-2 ${gameState.timeRemaining <= 10 ? 'border-red-500 bg-red-50 dark:bg-red-900/20' : 'border-green-500 bg-green-50 dark:bg-green-900/20'}`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-center gap-3">
                <Clock className={`h-6 w-6 ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-green-600'}`} />
                <div className="text-center">
                  <div className={`text-3xl font-bold ${gameState.timeRemaining <= 10 ? 'text-red-600' : 'text-green-600'}`}>
                    0:{gameState.timeRemaining.toString().padStart(2, '0')}
                  </div>
                  <div className={`text-sm ${gameState.timeRemaining <= 10 ? 'text-red-500' : 'text-green-500'}`}>
                    Steal Time Remaining
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 dark:border-green-800">
            <CardHeader className="bg-green-50 dark:bg-green-900/20">
              <CardTitle className="text-center text-green-800 dark:text-green-300">
                Steal Opportunity!
              </CardTitle>
              <p className="text-center text-green-600 dark:text-green-400">
                The high bidder answered incorrectly. You can steal {gameState.wagers[gameState.highBidder!]} points with the correct answer!
              </p>
            </CardHeader>
            <CardContent className="space-y-4 pt-6">
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>

              <div className="grid grid-cols-1 gap-3">
                {['A', 'B', 'C', 'D'].map((choice) => {
                  const isHighBidderAnswer = choice === gameState.highBidderAnswer;
                  return (
                    <Button
                      key={choice}
                      variant={currentAnswer === choice ? "default" : "outline"}
                      className={`text-left justify-start p-4 h-auto ${
                        isHighBidderAnswer
                          ? 'opacity-50 bg-gray-200 dark:bg-gray-700 cursor-not-allowed'
                          : ''
                      }`}
                      onClick={() => !isHighBidderAnswer && setCurrentAnswer(choice)}
                      disabled={isHighBidderAnswer}
                    >
                      <span className="font-bold mr-3">{choice}.</span>
                      <span>{gameState.currentQuestion?.[`choice${choice}` as keyof Question] as string}</span>
                      {isHighBidderAnswer && (
                        <Badge className="ml-auto bg-red-500">Already Chosen</Badge>
                      )}
                    </Button>
                  );
                })}
              </div>

              <Button
                onClick={submitSteal}
                className={`w-full ${
                  gameState.timeRemaining <= 10
                    ? 'bg-red-600 hover:bg-red-700 animate-pulse'
                    : 'bg-green-600 hover:bg-green-700'
                }`}
                disabled={!currentAnswer}
              >
                <Star className="h-4 w-4 mr-2" />
                {gameState.timeRemaining <= 10 ? 'Steal Now!' : `Attempt Steal (+${gameState.wagers[gameState.highBidder!]} points)`}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Result Phase */}
      {gameState.phase === 'result' && (
        <div className="space-y-6">
          {/* Player Scores */}
          <div className="grid grid-cols-2 gap-4">
            {gameState.players.map((player, index) => (
              <Card key={index} className={`${index === gameState.roundWinner ? 'ring-2 ring-yellow-500' : ''}`}>
                <CardContent className="p-4 text-center">
                  <div className="flex items-center justify-center gap-2 mb-2">
                    <Trophy className={`h-5 w-5 ${index === gameState.roundWinner ? 'text-yellow-500' : 'text-gray-400'}`} />
                    <h3 className="font-bold text-lg">{player.name}</h3>
                    {index === gameState.roundWinner && (
                      <Badge className="bg-yellow-500">Round Winner</Badge>
                    )}
                  </div>
                  <div className="text-2xl font-bold text-blue-600">{player.score}</div>
                  <div className="text-sm text-gray-500">points</div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Question and Answer Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Round {gameState.currentRound} - Answer Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Question */}
              <div className="text-lg font-medium text-center p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
                {gameState.currentQuestion?.question}
              </div>

              {/* Loading State */}
              {gameState.isLoadingExplanation && (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <p className="text-gray-500 dark:text-gray-400">Generating detailed explanation...</p>
                </div>
              )}

              {/* Answer Choices with Explanations */}
              {!gameState.isLoadingExplanation && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white text-center mb-4">
                    Answer Analysis
                  </h3>

                  <div className="grid gap-4">
                    {['A', 'B', 'C', 'D'].map((choice) => {
                      const isCorrect = choice === gameState.correctAnswer;
                      const choiceExplanation = gameState.questionExplanation?.choiceExplanations.find(
                        exp => exp.choice === choice
                      );

                      return (
                        <div
                          key={choice}
                          className={`p-4 rounded-lg border-2 ${
                            isCorrect
                              ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                              : 'border-red-300 bg-red-50 dark:bg-red-900/20'
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                              isCorrect ? 'bg-green-500' : 'bg-red-500'
                            }`}>
                              {choice}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <p className="font-medium text-gray-900 dark:text-white">
                                  {gameState.currentQuestion?.[`choice${choice}` as keyof Question] as string}
                                </p>
                                <Badge className={isCorrect ? 'bg-green-500' : 'bg-red-500'}>
                                  {isCorrect ? 'Correct' : 'Incorrect'}
                                </Badge>
                              </div>

                              {choiceExplanation ? (
                                <div className="space-y-2">
                                  <p className={`text-sm ${
                                    isCorrect ? 'text-green-800 dark:text-green-300' : 'text-red-800 dark:text-red-300'
                                  }`}>
                                    <strong>Why this choice is {isCorrect ? 'correct' : 'incorrect'}:</strong>
                                  </p>
                                  <p className={`text-sm ${
                                    isCorrect ? 'text-green-700 dark:text-green-400' : 'text-red-700 dark:text-red-400'
                                  }`}>
                                    {choiceExplanation.reasoning}
                                  </p>
                                  <p className={`text-sm ${
                                    isCorrect ? 'text-green-600 dark:text-green-500' : 'text-red-600 dark:text-red-500'
                                  }`}>
                                    {choiceExplanation.explanation}
                                  </p>
                                </div>
                              ) : (
                                <p className={`text-sm ${
                                  isCorrect ? 'text-green-700 dark:text-green-400' : 'text-red-700 dark:text-red-400'
                                }`}>
                                  {isCorrect ? 'This is the correct answer.' : 'This answer is incorrect.'}
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Additional Learning Content */}
              {gameState.questionExplanation && !gameState.isLoadingExplanation && (
                <div className="space-y-4">
                  {/* Overall Explanation */}
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2 flex items-center gap-2">
                      <Star className="h-4 w-4" />
                      Key Concept
                    </h4>
                    <p className="text-blue-800 dark:text-blue-400">{gameState.questionExplanation.explanation}</p>
                  </div>

                  {/* Key Points */}
                  {gameState.questionExplanation.keyPoints.length > 0 && (
                    <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                      <h4 className="font-semibold text-purple-900 dark:text-purple-300 mb-2">Key Learning Points:</h4>
                      <ul className="text-purple-800 dark:text-purple-400 space-y-1">
                        {gameState.questionExplanation.keyPoints.map((point, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                            {point}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Additional Learning Content - Collapsible */}
                  {(gameState.questionExplanation.studyTips.length > 0 || gameState.questionExplanation.relatedConcepts.length > 0) && (
                    <div className="border border-gray-200 dark:border-gray-700 rounded-lg">
                      <button
                        onClick={() => setShowAdditionalLearning(!showAdditionalLearning)}
                        className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                      >
                        <div className="flex items-center gap-2">
                          <BookOpen className="h-5 w-5 text-indigo-600" />
                          <span className="font-semibold text-gray-900 dark:text-white">
                            Additional Learning Resources
                          </span>
                          <Badge variant="secondary" className="text-xs">
                            {gameState.questionExplanation.difficulty}
                          </Badge>
                        </div>
                        {showAdditionalLearning ? (
                          <ChevronUp className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        )}
                      </button>

                      {showAdditionalLearning && (
                        <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-4">
                          {/* Study Tips */}
                          {gameState.questionExplanation.studyTips.length > 0 && (
                            <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                              <h5 className="font-semibold text-yellow-900 dark:text-yellow-300 mb-2 flex items-center gap-2">
                                <Lightbulb className="h-4 w-4" />
                                Study Tips
                              </h5>
                              <ul className="text-yellow-800 dark:text-yellow-400 space-y-2">
                                {gameState.questionExplanation.studyTips.map((tip, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <div className="w-1.5 h-1.5 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></div>
                                    {tip}
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {/* Related Concepts */}
                          {gameState.questionExplanation.relatedConcepts.length > 0 && (
                            <div className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg">
                              <h5 className="font-semibold text-indigo-900 dark:text-indigo-300 mb-2 flex items-center gap-2">
                                <Target className="h-4 w-4" />
                                Related GDPR Concepts
                              </h5>
                              <div className="flex flex-wrap gap-2">
                                {gameState.questionExplanation.relatedConcepts.map((concept, index) => (
                                  <Badge key={index} variant="outline" className="text-indigo-700 dark:text-indigo-300 border-indigo-300">
                                    {concept}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}

              {/* Basic explanation fallback */}
              {!gameState.questionExplanation && !gameState.isLoadingExplanation && gameState.currentQuestion?.explanation && (
                <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-900 dark:text-blue-300 mb-2">Explanation:</h4>
                  <p className="text-blue-800 dark:text-blue-400">{gameState.currentQuestion.explanation}</p>
                </div>
              )}

              <Button onClick={nextRound} className="w-full" disabled={gameState.isLoadingExplanation}>
                {gameState.currentRound >= gameState.totalRounds ? (
                  <>
                    <Trophy className="h-4 w-4 mr-2" />
                    View Final Results
                  </>
                ) : (
                  <>
                    <Zap className="h-4 w-4 mr-2" />
                    Next Round
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Game Over Phase */}
      {gameState.phase === 'gameOver' && (
        <div className="space-y-6">
          <Card className="border-yellow-200 dark:border-yellow-800">
            <CardHeader className="bg-yellow-50 dark:bg-yellow-900/20 text-center">
              <CardTitle className="text-2xl font-bold text-yellow-800 dark:text-yellow-300">
                🎉 Game Over! 🎉
              </CardTitle>
              {gameState.gameWinner !== null ? (
                <p className="text-yellow-600 dark:text-yellow-400">
                  {gameState.players[gameState.gameWinner].name} Wins!
                </p>
              ) : (
                <p className="text-yellow-600 dark:text-yellow-400">
                  It&apos;s a Tie!
                </p>
              )}
            </CardHeader>
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-4 mb-6">
                {gameState.players.map((player, index) => (
                  <Card key={index} className={`${index === gameState.gameWinner ? 'ring-2 ring-yellow-500' : ''}`}>
                    <CardContent className="p-4 text-center">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Crown className={`h-6 w-6 ${index === gameState.gameWinner ? 'text-yellow-500' : 'text-gray-400'}`} />
                        <h3 className="font-bold text-xl">{player.name}</h3>
                      </div>
                      <div className="text-3xl font-bold text-blue-600">{player.score}</div>
                      <div className="text-sm text-gray-500">final score</div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="flex gap-4">
                <Button onClick={resetGame} className="flex-1">
                  <Play className="h-4 w-4 mr-2" />
                  Play Again
                </Button>
                <Button onClick={onBack} variant="outline" className="flex-1">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Games
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      </div>
    </>
  );
}

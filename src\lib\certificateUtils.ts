/**
 * Utility functions for certificate-related operations
 */

import { Certificate } from '@/Firebase/firestore/services/CertificatesService';

/**
 * Generate a URL-friendly slug from a certificate name
 */
export function generateCertificateSlug(certificateName: string): string {
  return certificateName
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Generate the full certificate detail URL
 */
export function generateCertificateUrl(lang: string, certificateName: string): string {
  const slug = generateCertificateSlug(certificateName);
  return `/${lang}/portal/certificates/${encodeURIComponent(slug)}`;
}

/**
 * Match a certificate by name or slug
 */
export function matchCertificateBySlug(certificates: Certificate[], slug: string): Certificate | null {
  const decodedSlug = decodeURIComponent(slug);
  
  return certificates.find(cert => {
    const certSlug = generateCertificateSlug(cert.name);
    return certSlug === decodedSlug.toLowerCase() || 
           cert.name.toLowerCase() === decodedSlug.toLowerCase();
  }) || null;
}

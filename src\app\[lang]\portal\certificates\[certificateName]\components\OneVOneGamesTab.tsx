"use client";

import React, { useState } from "react";
import { Certificate } from "@/Firebase/firestore/services/CertificatesService";
import {
  Gamepad2,
  Users,
  Trophy,
  Play,
  Star,
  Target,
  Zap,
  Map
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import PrivacyStakeGame from "./games/PrivacyStakeGame";
import DomainDominationGame from "./games/DomainDominationGame";

interface OneVOneGamesTabProps {
  certificate: Certificate;
}

export default function OneVOneGamesTab({ certificate }: OneVOneGamesTabProps) {
  const [selectedGame, setSelectedGame] = useState<string | null>(null);

  const games = [
    {
      id: "privacy-stake",
      name: "Privacy Stake",
      description: "A strategic 1v1 quiz battle where players wager points based on their confidence in GDPR knowledge.",
      difficulty: "Medium",
      estimatedTime: "15-20 minutes",
      maxPlayers: 2,
      features: [
        "Strategic wagering system",
        "Steal opportunities",
        "Real-time scoring",
        "GDPR question bank"
      ],
      icon: Target,
      color: "from-blue-500 to-indigo-600"
    },
    {
      id: "domain-domination",
      name: "GDPR Domain Domination",
      description: "A strategic territory conquest game where players battle to control GDPR domains through knowledge mastery.",
      difficulty: "Hard",
      estimatedTime: "20-30 minutes",
      maxPlayers: 2,
      features: [
        "Territory-based gameplay",
        "Domain specialization",
        "Capture & fortify mechanics",
        "Strategic depth"
      ],
      icon: Map,
      color: "from-purple-500 to-pink-600"
    }
  ];

  if (selectedGame) {
    const game = games.find(g => g.id === selectedGame);
    if (game?.id === "privacy-stake") {
      return (
        <PrivacyStakeGame
          certificate={certificate}
          onBack={() => setSelectedGame(null)}
        />
      );
    }
    if (game?.id === "domain-domination") {
      return (
        <DomainDominationGame
          certificate={certificate}
          onBack={() => setSelectedGame(null)}
        />
      );
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
      {/* Header */}
      <div className="mb-6 sm:mb-8">
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 mb-4">
          <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-2 sm:p-3 rounded-xl shadow-lg">
            <Gamepad2 className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">
              1V1 Games
            </h2>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">
              Challenge yourself and others with interactive learning games
            </p>
          </div>
        </div>
      </div>

      {/* Games Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
        {games.map((game) => {
          const IconComponent = game.icon;
          return (
            <Card key={game.id} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-start justify-between mb-3">
                  <div className={`bg-gradient-to-r ${game.color} p-3 rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-6 w-6 text-white" />
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {game.difficulty}
                    </Badge>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                      <Users className="h-3 w-3" />
                      {game.maxPlayers}
                    </div>
                  </div>
                </div>
                <CardTitle className="text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {game.name}
                </CardTitle>
                <CardDescription className="text-gray-600 dark:text-gray-400 leading-relaxed">
                  {game.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                {/* Game Features */}
                <div className="mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-2 flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    Features
                  </h4>
                  <div className="space-y-1">
                    {game.features.map((feature, index) => (
                      <div key={index} className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                        {feature}
                      </div>
                    ))}
                  </div>
                </div>

                {/* Game Stats */}
                <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-white">
                      {game.estimatedTime}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Duration
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-gray-900 dark:text-white flex items-center gap-1">
                      <Trophy className="h-4 w-4 text-yellow-500" />
                      1000
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Starting Points
                    </div>
                  </div>
                </div>

                {/* Play Button */}
                <Button 
                  onClick={() => setSelectedGame(game.id)}
                  className="w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-2.5 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 group"
                >
                  <Play className="h-4 w-4 mr-2 group-hover:scale-110 transition-transform" />
                  Start Game
                  <Zap className="h-4 w-4 ml-2 group-hover:scale-110 transition-transform" />
                </Button>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Coming Soon Section */}
      <div className="mt-12 text-center">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-2xl p-8 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-gradient-to-r from-purple-500 to-pink-600 p-4 rounded-full shadow-lg">
              <Star className="h-8 w-8 text-white" />
            </div>
          </div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            More Games Coming Soon!
          </h3>
          <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
            We&apos;re working on exciting new game modes to make learning GDPR even more engaging and fun.
          </p>
        </div>
      </div>
    </div>
  );
}

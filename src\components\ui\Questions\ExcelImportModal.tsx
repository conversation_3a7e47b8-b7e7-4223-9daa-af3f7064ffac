"use client";

import React, { useState, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Upload, 
  Download, 
  FileSpreadsheet, 
  X, 
  AlertCircle, 
  CheckCircle,
  Info
} from "lucide-react";
import * as XLSX from 'xlsx';

interface ExcelQuestionRow {
  question: string;
  correctAnswer: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
}

interface ExcelImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onImport: (data: ExcelQuestionRow[], importMode: 'replace' | 'append') => Promise<void>;
  isImporting?: boolean;
}

export function ExcelImportModal({ 
  isOpen, 
  onClose, 
  onImport, 
  isImporting = false 
}: ExcelImportModalProps) {
  const [importMode, setImportMode] = useState<'replace' | 'append'>('append');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewData, setPreviewData] = useState<ExcelQuestionRow[]>([]);
  const [error, setError] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      setError("Please select a valid Excel file (.xlsx or .xls)");
      return;
    }

    setSelectedFile(file);
    setError("");
    
    // Read and preview the file
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target?.result as ArrayBuffer);
        const workbook = XLSX.read(data, { type: 'array' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        
        // Convert to JSON with header mapping
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
          header: ['question', 'correctAnswer', 'choiceA', 'choiceB', 'choiceC', 'choiceD'],
          range: 1 // Skip header row
        });
        
        // Validate and clean data
        const validData = jsonData
          .filter((row: unknown): row is ExcelQuestionRow => {
            if (!row || typeof row !== 'object' || row === null) return false;
            const record = row as Record<string, unknown>;
            return 'question' in record && 'correctAnswer' in record &&
                   typeof record.question === 'string' &&
                   typeof record.correctAnswer === 'string';
          })
          .slice(0, 5) as ExcelQuestionRow[]; // Show only first 5 rows for preview

        setPreviewData(validData);
      } catch (err) {
        setError("Failed to read Excel file. Please check the file format.");
        console.error("Excel read error:", err);
      }
    };
    reader.readAsArrayBuffer(file);
  };

  const handleImport = async () => {
    if (!selectedFile) return;

    try {
      const reader = new FileReader();
      reader.onload = async (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: 'array' });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: ['question', 'correctAnswer', 'choiceA', 'choiceB', 'choiceC', 'choiceD'],
            range: 1
          });
          
          const validData = jsonData.filter((row: unknown): row is ExcelQuestionRow => {
            if (!row || typeof row !== 'object' || row === null) return false;
            const record = row as Record<string, unknown>;
            return 'question' in record && 'correctAnswer' in record &&
                   typeof record.question === 'string' &&
                   typeof record.correctAnswer === 'string';
          }) as ExcelQuestionRow[];

          await onImport(validData, importMode);
          handleClose();
        } catch (err) {
          setError("Failed to import questions. Please try again.");
          console.error("Import error:", err);
        }
      };
      reader.readAsArrayBuffer(selectedFile);
    } catch (err) {
      setError("Failed to process file. Please try again.");
      console.error("File processing error:", err);
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setPreviewData([]);
    setError("");
    setImportMode('append');
    onClose();
  };

  const downloadTemplate = () => {
    const templateData = [
      {
        'Question': 'What is the capital of France?',
        'Correct Answer': 'B',
        'Choice A': 'London',
        'Choice B': 'Paris',
        'Choice C': 'Berlin',
        'Choice D': 'Madrid'
      },
      {
        'Question': 'Which planet is known as the Red Planet?',
        'Correct Answer': 'B',
        'Choice A': 'Venus',
        'Choice B': 'Mars',
        'Choice C': 'Jupiter',
        'Choice D': 'Saturn'
      },
      {
        'Question': 'What is 2 + 2?',
        'Correct Answer': 'A',
        'Choice A': '4',
        'Choice B': '3',
        'Choice C': '5',
        'Choice D': '6'
      }
    ];

    const ws = XLSX.utils.json_to_sheet(templateData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Questions Template");
    XLSX.writeFile(wb, "questions_template.xlsx");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] bg-white dark:bg-gray-800 border-0 shadow-2xl flex flex-col">
        <DialogHeader className="pb-6 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gradient-to-r from-green-600 to-emerald-600 p-2 rounded-lg">
                <FileSpreadsheet className="h-5 w-5 text-white" />
              </div>
              <DialogTitle className="text-xl font-bold text-gray-900 dark:text-white">
                Import Questions from Excel
              </DialogTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <div className="space-y-6 p-6">
          {/* Template Download */}
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Excel Template Format
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-200 mb-3">
                  Your Excel file should have the following columns in order:
                </p>
                <div className="grid grid-cols-2 gap-2 text-xs text-blue-600 dark:text-blue-300 mb-3">
                  <div>• Column A: Question</div>
                  <div>• Column B: Correct Answer (A, B, C, or D)</div>
                  <div>• Column C: Choice A</div>
                  <div>• Column D: Choice B</div>
                  <div>• Column E: Choice C</div>
                  <div>• Column F: Choice D</div>
                </div>
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
                  <p className="text-xs text-yellow-800 dark:text-yellow-200">
                    <strong>Note:</strong> The correct answer can be either the letter (A, B, C, D) or the exact text of one of the choices.
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={downloadTemplate}
                  className="text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Template
                </Button>
              </div>
            </div>
          </div>

          {/* Import Mode Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold text-gray-900 dark:text-white">
              Import Mode
            </Label>
            <RadioGroup value={importMode} onValueChange={(value: 'replace' | 'append') => setImportMode(value)}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="append" id="append" />
                <Label htmlFor="append" className="text-sm text-gray-700 dark:text-gray-300">
                  Add to existing questions (Append)
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="replace" id="replace" />
                <Label htmlFor="replace" className="text-sm text-gray-700 dark:text-gray-300">
                  Replace all existing questions
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* File Upload */}
          <div className="space-y-3">
            <Label className="text-sm font-semibold text-gray-900 dark:text-white">
              Select Excel File
            </Label>
            <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
              <input
                ref={fileInputRef}
                type="file"
                accept=".xlsx,.xls"
                onChange={handleFileSelect}
                className="hidden"
              />
              {selectedFile ? (
                <div className="space-y-2">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto" />
                  <p className="text-sm font-medium text-gray-900 dark:text-white">
                    {selectedFile.name}
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Choose Different File
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  <Upload className="h-8 w-8 text-gray-400 mx-auto" />
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Click to select an Excel file
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Select File
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

            {/* Preview */}
            {previewData.length > 0 && (
              <div className="space-y-3">
                <Label className="text-sm font-semibold text-gray-900 dark:text-white">
                  Preview (First 5 rows)
                </Label>
                <div className="overflow-x-auto max-h-60 overflow-y-auto border border-gray-200 dark:border-gray-700 rounded-lg">
                  <table className="w-full text-xs">
                    <thead className="bg-gray-50 dark:bg-gray-800 sticky top-0">
                      <tr>
                        <th className="p-2 text-left">Question</th>
                        <th className="p-2 text-left">Correct Answer</th>
                        <th className="p-2 text-left">Choice A</th>
                        <th className="p-2 text-left">Choice B</th>
                        <th className="p-2 text-left">Choice C</th>
                        <th className="p-2 text-left">Choice D</th>
                      </tr>
                    </thead>
                    <tbody>
                      {previewData.map((row, index) => (
                        <tr key={index} className="border-t border-gray-200 dark:border-gray-700">
                          <td className="p-2 max-w-xs truncate">{row.question}</td>
                          <td className="p-2">{row.correctAnswer}</td>
                          <td className="p-2">{row.choiceA}</td>
                          <td className="p-2">{row.choiceB}</td>
                          <td className="p-2">{row.choiceC}</td>
                          <td className="p-2">{row.choiceD}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Actions - Fixed at bottom */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 bg-white dark:bg-gray-800">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isImporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleImport}
            disabled={!selectedFile || isImporting}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
          >
            {isImporting ? "Importing..." : "Import Questions"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}

import React from "react";
import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import { Toaster } from "@/components/ui/toaster";
import PortalSidebar from "@/components/portal/PortalSidebar";
import MobilePortalLayout from "@/components/portal/MobilePortalLayout";

export default async function PortalLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ lang: Locale }>;
}) {
  const { lang } = await params;
  const dict = await getDictionary(lang);

  return (
    <>
      {/* Desktop Layout - Hidden on mobile */}
      <div className="hidden lg:block min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex" dir={lang === 'ar' ? 'rtl' : 'ltr'}>
          {/* Left Sidebar for LTR (English), Right for RTL (Arabic) */}
          {lang === 'ar' ? (
            <>
              {/* Main Content */}
              <main className="flex-1 min-h-screen">
                {children}
              </main>
              {/* Right Sidebar for Arabic */}
              <PortalSidebar dict={dict} />
            </>
          ) : (
            <>
              {/* Left Sidebar for English */}
              <PortalSidebar dict={dict} />
              {/* Main Content */}
              <main className="flex-1 min-h-screen">
                {children}
              </main>
            </>
          )}
        </div>
      </div>

      {/* Mobile Layout - Visible on mobile */}
      <div className="lg:hidden">
        <MobilePortalLayout dict={dict} lang={lang}>
          {children}
        </MobilePortalLayout>
      </div>

      {/* Toast notifications */}
      <Toaster />
    </>
  );
}

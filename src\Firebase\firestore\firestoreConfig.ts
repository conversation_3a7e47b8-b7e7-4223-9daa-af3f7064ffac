import {
  getFirestore,
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  DocumentData,
  QueryConstraint,
  enableNetwork,
  Firestore
} from 'firebase/firestore';
import { firebaseApp } from '../config';

// Initialize Firestore with error handling
let firestore: Firestore;

try {
  firestore = getFirestore(firebaseApp);

  // Enable offline persistence and better error handling
  if (typeof window !== 'undefined') {
    // Only run in browser environment
    enableNetwork(firestore).catch((error) => {
      console.warn('Firestore network enable failed:', error);
    });
  }
} catch (error) {
  console.error('Failed to initialize Firestore:', error);
  throw error;
}

// Helper functions with error handling
const createDocument = async (
  collectionPath: string,
  docId: string,
  data: DocumentData
): Promise<void> => {
  try {
    const docRef = doc(firestore, collectionPath, docId);
    await setDoc(docRef, data);
  } catch (error) {
    console.error('Error creating document:', error);
    throw error;
  }
};

const getDocument = async (
  collectionPath: string,
  docId: string
): Promise<DocumentData | null> => {
  try {
    const docRef = doc(firestore, collectionPath, docId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return { id: docSnap.id, ...docSnap.data() };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting document:', error);
    throw error;
  }
};

const updateDocument = async (
  collectionPath: string, 
  docId: string, 
  data: DocumentData
): Promise<void> => {
  const docRef = doc(firestore, collectionPath, docId);
  await updateDoc(docRef, data);
};

const deleteDocument = async (
  collectionPath: string, 
  docId: string
): Promise<void> => {
  const docRef = doc(firestore, collectionPath, docId);
  await deleteDoc(docRef);
};

const queryDocuments = async (
  collectionPath: string,
  constraints: QueryConstraint[] = []
): Promise<DocumentData[]> => {
  const collectionRef = collection(firestore, collectionPath);
  const q = query(collectionRef, ...constraints);
  const querySnapshot = await getDocs(q);
  
  return querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() }));
};

export {
  firestore,
  createDocument,
  getDocument,
  updateDocument,
  deleteDocument,
  queryDocuments,
  collection,
  doc,
  query,
  where,
  orderBy,
  limit
}; 
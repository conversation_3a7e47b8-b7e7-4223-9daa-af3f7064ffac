"use client";

import React, { useState } from "react";
import { Dictionary } from "@/dictionaries";
import { Locale } from "@/i18n-config";
import { Button } from "@/components/ui/button";
import { Sheet, Sheet<PERSON>ontent, Sheet<PERSON>rigger, SheetTitle } from "@/components/ui/sheet";
import { Menu, GraduationCap, Home, Award, Settings, LogOut, Trophy } from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useAuth } from "@/context";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface MobilePortalLayoutProps {
  children: React.ReactNode;
  dict: Dictionary;
  lang: Locale;
}

const getNavigationItems = (lang: string) => [
  {
    name: "Home",
    href: `/${lang}/portal/home`,
    icon: Home,
    description: "Dashboard overview"
  },
  {
    name: "Certificates",
    href: `/${lang}/portal/certificates`,
    icon: Award,
    description: "Manage certifications"
  }
];

export default function MobilePortalLayout({ children, lang }: MobilePortalLayoutProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout } = useAuth();
  
  const navigationItems = getNavigationItems(lang);

  const handleSignOut = async () => {
    try {
      setIsLoggingOut(true);
      await logout();
      router.push(`/${lang}/auth/signin`);
    } catch (error) {
      console.error("Sign out error:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const getCurrentPageTitle = () => {
    const currentItem = navigationItems.find(item => pathname === item.href);
    return currentItem?.name || "Portal";
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile Header */}
      <header className="sticky top-0 z-40 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
        <div className="flex items-center justify-between px-4 py-3">
          <div className="flex items-center space-x-3">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80 p-0">
                <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
                <div className="flex flex-col h-full bg-white dark:bg-gray-800">
                  {/* Mobile Sidebar Header */}
                  <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl blur-sm opacity-30"></div>
                        <div className="relative bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl shadow-lg">
                          <GraduationCap className="h-7 w-7 text-white" />
                        </div>
                      </div>
                      <div>
                        <h1 className="text-xl font-bold text-gray-900 dark:text-white">
                          Certification Portal
                        </h1>
                        <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">
                          Your study companion
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* User Profile */}
                  <div className="p-6">
                    <div className="bg-gradient-to-br from-white/90 to-blue-50/90 dark:from-gray-800/90 dark:to-blue-900/20 backdrop-blur-sm rounded-2xl p-5 shadow-lg">
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-14 w-14 ring-3 ring-blue-200/50 dark:ring-blue-700/50 shadow-lg">
                          {user?.photoURL ? (
                            <AvatarImage src={user.photoURL} alt={user.displayName || "User"} />
                          ) : (
                            <AvatarFallback className="bg-gradient-to-br from-blue-600 to-indigo-600 text-white font-bold text-lg">
                              {user?.displayName ? user.displayName.charAt(0).toUpperCase() :
                               user?.email ? user.email.charAt(0).toUpperCase() : "U"}
                            </AvatarFallback>
                          )}
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="text-base font-semibold text-gray-900 dark:text-white truncate">
                            {user?.displayName || "User"}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                            {user?.email}
                          </p>
                          <div className="flex items-center mt-2">
                            <div className="bg-blue-100/80 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                              <Trophy className="h-3 w-3 mr-1" />
                              Active Learner
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Navigation */}
                  <div className="flex-1 px-6">
                    <div className="mb-4">
                      <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider px-2">
                        Navigation
                      </h3>
                    </div>
                    <nav className="space-y-1">
                      {navigationItems.map((item) => {
                        const isActive = pathname === item.href;
                        return (
                          <Link
                            key={item.name}
                            href={item.href}
                            onClick={() => setIsOpen(false)}
                            className={cn(
                              "flex items-center space-x-3 px-4 py-3.5 rounded-xl transition-all duration-200 group relative",
                              isActive
                                ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg transform scale-[1.02]"
                                : "text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-gray-700/50 hover:text-blue-700 dark:hover:text-white hover:shadow-md"
                            )}
                          >
                            {isActive && (
                              <div className="absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full"></div>
                            )}
                            <item.icon className={cn(
                              "h-5 w-5 transition-colors flex-shrink-0",
                              isActive ? "text-white" : "text-gray-500 group-hover:text-blue-600 dark:group-hover:text-gray-300"
                            )} />
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-semibold">{item.name}</div>
                              <div className={cn(
                                "text-xs mt-0.5",
                                isActive ? "text-blue-100" : "text-gray-500 dark:text-gray-400 group-hover:text-blue-500"
                              )}>
                                {item.description}
                              </div>
                            </div>
                          </Link>
                        );
                      })}
                    </nav>
                  </div>

                  {/* Footer Actions */}
                  <div className="p-6 space-y-3 bg-gradient-to-t from-gray-50 to-transparent dark:from-gray-800/50 dark:to-transparent border-t border-gray-200 dark:border-gray-700">
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-gray-700 dark:text-gray-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 transition-all duration-200 py-3 rounded-xl"
                    >
                      <Settings className="h-4 w-4 mr-3" />
                      <span className="font-medium">Settings</span>
                    </Button>
                    <Button
                      variant="ghost"
                      onClick={handleSignOut}
                      disabled={isLoggingOut}
                      className="w-full justify-start text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-400 transition-all duration-200 py-3 rounded-xl"
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      <span className="font-medium">
                        {isLoggingOut ? "Signing out..." : "Sign Out"}
                      </span>
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            
            <div className="flex items-center space-x-2">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-2 rounded-lg">
                <GraduationCap className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                {getCurrentPageTitle()}
              </h1>
            </div>
          </div>

          {/* User Avatar in Header */}
          <Avatar className="h-8 w-8">
            {user?.photoURL ? (
              <AvatarImage src={user.photoURL} alt={user.displayName || "User"} />
            ) : (
              <AvatarFallback className="bg-gradient-to-br from-blue-600 to-indigo-600 text-white text-sm font-medium">
                {user?.displayName ? user.displayName.charAt(0).toUpperCase() :
                 user?.email ? user.email.charAt(0).toUpperCase() : "U"}
              </AvatarFallback>
            )}
          </Avatar>
        </div>
      </header>

      {/* Main Content */}
      <main className="min-h-screen">
        {children}
      </main>
    </div>
  );
}

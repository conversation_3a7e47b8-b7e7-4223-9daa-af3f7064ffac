@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

:root {
  --radius: 0.625rem;
  /* New color palette */
  --black: #000000;
  --ultra-violet: #564D80;
  --white: #FCFCFC;
  --light-sea-green: #00BFB2;
  --dodger-blue: #1098F7;
  
  /* Main theme colors using the new palette */
  --background: var(--white);
  --foreground: var(--black);
  --card: var(--white);
  --card-foreground: var(--black);
  --popover: var(--white);
  --popover-foreground: var(--black);
  --primary: var(--ultra-violet);
  --primary-foreground: var(--white);
  --secondary: var(--light-sea-green);
  --secondary-foreground: var(--white);
  --muted: var(--light-sea-green);
  --muted-foreground: var(--black);
  --accent: var(--dodger-blue);
  --accent-foreground: var(--white);
  --destructive: #FF3B30;
  --border: var(--ultra-violet);
  --input: var(--white);
  --ring: var(--dodger-blue);
  
  /* Chart colors */
  --chart-1: var(--ultra-violet);
  --chart-2: var(--light-sea-green);
  --chart-3: var(--dodger-blue);
  --chart-4: #A78BFA;
  --chart-5: #F59E0B;
  
  /* Sidebar colors */
  --sidebar: var(--ultra-violet);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--light-sea-green);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--dodger-blue);
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: var(--light-sea-green);
  --sidebar-ring: var(--dodger-blue);
}

.dark {
  /* Dark theme using the new palette */
  --background: var(--black);
  --foreground: var(--white);
  --card: var(--ultra-violet);
  --card-foreground: var(--white);
  --popover: var(--ultra-violet);
  --popover-foreground: var(--white);
  --primary: var(--light-sea-green);
  --primary-foreground: var(--black);
  --secondary: var(--dodger-blue);
  --secondary-foreground: var(--white);
  --muted: var(--ultra-violet);
  --muted-foreground: var(--light-sea-green);
  --accent: var(--light-sea-green);
  --accent-foreground: var(--white);
  --destructive: #FF453A;
  --border: var(--light-sea-green);
  --input: rgba(252, 252, 252, 0.15);
  --ring: var(--dodger-blue);
  
  /* Chart colors for dark mode */
  --chart-1: var(--light-sea-green);
  --chart-2: var(--dodger-blue);
  --chart-3: var(--ultra-violet);
  --chart-4: #C4B5FD;
  --chart-5: #FBBF24;
  
  /* Sidebar colors for dark mode */
  --sidebar: var(--black);
  --sidebar-foreground: var(--white);
  --sidebar-primary: var(--light-sea-green);
  --sidebar-primary-foreground: var(--white);
  --sidebar-accent: var(--dodger-blue);
  --sidebar-accent-foreground: var(--white);
  --sidebar-border: rgba(252, 252, 252, 0.1);
  --sidebar-ring: var(--light-sea-green);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

@layer utilities {
  /* Custom grid columns for question numbers */
  .grid-cols-14 {
    grid-template-columns: repeat(14, minmax(0, 1fr));
  }
  .grid-cols-15 {
    grid-template-columns: repeat(15, minmax(0, 1fr));
  }
  .grid-cols-20 {
    grid-template-columns: repeat(20, minmax(0, 1fr));
  }
}

import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import CertificateDetailClient from "./CertificateDetailClient";

export default async function CertificateDetailPage({
  params
}: {
  params: Promise<{ lang: Locale; certificateName: string }>
}) {
  const { lang, certificateName } = await params;
  const dict = await getDictionary(lang);

  return <CertificateDetailClient dict={dict} certificateName={certificateName} />;
}

"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Award, Calendar, Target, Loader2 } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { AddCertificateModal } from "@/components/ui/Certificates/AddCertificateModal";
import { useAuth } from "@/context";
import { useToast } from "@/components/ui/use-toast";
import {
  createCertificate,
  getUserCertificates,
  Certificate,
  CreateCertificateData
} from "@/Firebase/firestore/services/CertificatesService";
import { generateCertificateUrl } from "@/lib/certificateUtils";

interface CertificatesClientProps {
  dict?: Record<string, unknown>;
}

export default function CertificatesClient({ }: CertificatesClientProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { user } = useAuth();
  const { toast } = useToast();
  const pathname = usePathname();
  const lang = pathname.split('/')[1];

  const loadCertificates = useCallback(async () => {
    if (!user?.uid) return;

    try {
      setIsLoading(true);
      const userCertificates = await getUserCertificates(user.uid);
      setCertificates(userCertificates);
    } catch (error) {
      console.error('Error loading certificates:', error);
      toast({
        title: "Error",
        description: "Failed to load certificates. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user?.uid, toast]);

  // Load certificates on component mount
  useEffect(() => {
    if (user?.uid) {
      loadCertificates();
    }
  }, [user?.uid, loadCertificates]);

  const handleAddCertificate = async (certificateData: CreateCertificateData) => {
    if (!user?.uid) {
      toast({
        title: "Error",
        description: "You must be logged in to add certificates.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      await createCertificate(user.uid, certificateData);

      toast({
        title: "Success",
        description: "Certificate added successfully!",
        variant: "default",
      });

      // Reload certificates to get the latest data
      await loadCertificates();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Error adding certificate:', error);
      toast({
        title: "Error",
        description: "Failed to add certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-indigo-50 dark:from-gray-900 dark:to-indigo-900/20 p-4 sm:p-6 lg:p-8">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6 sm:mb-8">
          <div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-2">
              My Certificates
            </h1>
            <p className="text-base sm:text-lg lg:text-xl text-gray-600 dark:text-gray-400">
              Track and manage your certification journey
            </p>
          </div>
          <Button
            onClick={() => setIsModalOpen(true)}
            className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-lg w-full sm:w-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Certificate
          </Button>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12 sm:py-20">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400">Loading certificates...</p>
            </div>
          </div>
        ) : certificates.length > 0 ? (
          /* Certificates Grid */
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {certificates.map((cert) => {
              const getStatusColor = (status: string) => {
                switch (status) {
                  case 'completed': return 'text-green-600';
                  case 'studying': return 'text-blue-600';
                  case 'scheduled': return 'text-purple-600';
                  case 'expired': return 'text-red-600';
                  default: return 'text-orange-600';
                }
              };

              const getPriorityColor = (priority: string) => {
                switch (priority) {
                  case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
                  case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
                  default: return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
                }
              };

              const certificateUrl = generateCertificateUrl(lang, cert.name);

              return (
                <Link
                  key={cert.id}
                  href={certificateUrl}
                  className="block bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 shadow-xl hover:shadow-2xl transition-all duration-300 border border-white/20 hover:scale-[1.02] cursor-pointer"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 rounded-xl">
                      <Award className="h-6 w-6 text-white" />
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-gray-500 dark:text-gray-400">Status</div>
                      <div className={`text-sm font-medium capitalize ${getStatusColor(cert.status)}`}>
                        {cert.status.replace('_', ' ')}
                      </div>
                    </div>
                  </div>

                  <h3 className="text-base sm:text-lg font-bold text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {cert.name}
                  </h3>

                  {cert.provider && (
                    <p className="text-xs sm:text-sm text-blue-600 dark:text-blue-400 mb-2 font-medium">
                      {cert.provider}
                    </p>
                  )}

                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2 sm:line-clamp-3">
                    {cert.description}
                  </p>

                  <div className="space-y-2 mb-4">
                    {cert.targetDate && (
                      <div className="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                        <Target className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                        <span className="truncate">Target: {new Date(cert.targetDate).toLocaleDateString()}</span>
                      </div>
                    )}
                    <div className="flex items-center text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2 flex-shrink-0" />
                      <span className="truncate">Added: {cert.createdAt?.toDate?.()?.toLocaleDateString() || 'Recently'}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className={`px-2 py-1 rounded-full text-xs font-medium capitalize ${getPriorityColor(cert.priority)}`}>
                      {cert.priority} Priority
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        ) : (
          /* Empty State */
          <div className="text-center py-12 sm:py-20">
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl p-8 sm:p-12 shadow-xl max-w-md mx-auto">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 p-3 sm:p-4 rounded-full w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6">
                <Award className="h-6 w-6 sm:h-8 sm:w-8 text-white mx-auto" />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-gray-900 dark:text-white mb-3 sm:mb-4">
                No certificates yet
              </h3>
              <p className="text-sm sm:text-base text-gray-500 dark:text-gray-400 mb-4 sm:mb-6 px-2">
                Start your certification journey by adding your first certificate goal
              </p>
              <Button
                onClick={() => setIsModalOpen(true)}
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 w-full sm:w-auto"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Your First Certificate
              </Button>
            </div>
          </div>
        )}

        {/* Add Certificate Modal */}
        <AddCertificateModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          onSubmit={handleAddCertificate}
          isSubmitting={isSubmitting}
        />
      </div>
    </div>
  );
}

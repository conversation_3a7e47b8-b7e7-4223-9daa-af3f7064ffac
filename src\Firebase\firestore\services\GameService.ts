import {
  collection,
  doc,
  addDoc,
  getDocs,
  updateDoc,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore';
import { firestore } from '../firestoreConfig';

export interface GameAttempt {
  id?: string;
  userId: string;
  certificateId: string;
  gameType: 'privacy-stake' | 'domain-domination';
  player1Name: string;
  player2Name: string;
  player1Score: number;
  player2Score: number;
  winner: 0 | 1 | null; // 0 = player1, 1 = player2, null = tie
  totalRounds: number;
  completedRounds: number;
  status: 'in_progress' | 'completed' | 'abandoned';
  startedAt: Timestamp;
  completedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface GameQuestion {
  id?: string;
  questionId: string;
  questionText: string;
  choiceA: string;
  choiceB: string;
  choiceC: string;
  choiceD: string;
  correctAnswer: 'A' | 'B' | 'C' | 'D';
  roundNumber: number;
  highBidder: 0 | 1; // Which player was the high bidder
  highBidderWager: number;
  highBidderAnswer?: 'A' | 'B' | 'C' | 'D';
  highBidderCorrect?: boolean;
  stealerAnswer?: 'A' | 'B' | 'C' | 'D';
  stealerCorrect?: boolean;
  roundWinner: 0 | 1 | null;
  timeSpentAnswer?: number; // seconds spent on answer phase
  timeSpentSteal?: number; // seconds spent on steal phase
  topic?: string;
  explanation?: string;
  answeredAt: Timestamp;
}

const GAME_ATTEMPTS_COLLECTION = 'gameAttempts';
const GAME_QUESTIONS_SUBCOLLECTION = 'questions';

/**
 * Create a new game attempt
 */
export const createGameAttempt = async (
  userId: string,
  certificateId: string,
  gameType: 'privacy-stake' | 'domain-domination',
  player1Name: string,
  player2Name: string,
  totalRounds: number
): Promise<string> => {
  try {
    const attemptData: Omit<GameAttempt, 'id'> = {
      userId,
      certificateId,
      gameType,
      player1Name,
      player2Name,
      player1Score: 1000,
      player2Score: 1000,
      winner: null,
      totalRounds,
      completedRounds: 0,
      status: 'in_progress',
      startedAt: serverTimestamp() as Timestamp,
      createdAt: serverTimestamp() as Timestamp,
      updatedAt: serverTimestamp() as Timestamp,
    };

    const docRef = await addDoc(collection(firestore, GAME_ATTEMPTS_COLLECTION), attemptData);
    return docRef.id;
  } catch (error) {
    console.error('Error creating game attempt:', error);
    throw new Error('Failed to create game attempt');
  }
};

/**
 * Add a question result to a game attempt
 */
export const addGameQuestionResult = async (
  attemptId: string,
  questionData: Omit<GameQuestion, 'id' | 'answeredAt'>
): Promise<string> => {
  try {
    const attemptRef = doc(firestore, GAME_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, GAME_QUESTIONS_SUBCOLLECTION);

    // Filter out undefined values to prevent Firebase errors
    const cleanedData = Object.fromEntries(
      Object.entries(questionData).filter(([, value]) => value !== undefined)
    );

    const gameQuestion: Omit<GameQuestion, 'id'> = {
      ...cleanedData,
      answeredAt: serverTimestamp() as Timestamp,
    } as Omit<GameQuestion, 'id'>;

    const docRef = await addDoc(questionsRef, gameQuestion);
    return docRef.id;
  } catch (error) {
    console.error('Error adding game question result:', error);
    throw new Error('Failed to add game question result');
  }
};

/**
 * Update game attempt with final results
 */
export const completeGameAttempt = async (
  attemptId: string,
  player1Score: number,
  player2Score: number,
  completedRounds: number
): Promise<void> => {
  try {
    const attemptRef = doc(firestore, GAME_ATTEMPTS_COLLECTION, attemptId);
    
    const winner = player1Score > player2Score ? 0 : 
                  player2Score > player1Score ? 1 : null;

    await updateDoc(attemptRef, {
      player1Score,
      player2Score,
      winner,
      completedRounds,
      status: 'completed',
      completedAt: serverTimestamp(),
      updatedAt: serverTimestamp(),
    });
  } catch (error) {
    console.error('Error completing game attempt:', error);
    throw new Error('Failed to complete game attempt');
  }
};

/**
 * Get all game attempts for a user and certificate
 */
export const getUserGameAttempts = async (
  userId: string,
  certificateId: string
): Promise<GameAttempt[]> => {
  try {
    const q = query(
      collection(firestore, GAME_ATTEMPTS_COLLECTION),
      where('userId', '==', userId),
      where('certificateId', '==', certificateId),
      orderBy('createdAt', 'desc')
    );

    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as GameAttempt));
  } catch (error) {
    console.error('Error fetching game attempts:', error);
    throw new Error('Failed to fetch game attempts');
  }
};

/**
 * Get all questions from a game attempt
 */
export const getGameQuestions = async (attemptId: string): Promise<GameQuestion[]> => {
  try {
    const attemptRef = doc(firestore, GAME_ATTEMPTS_COLLECTION, attemptId);
    const questionsRef = collection(attemptRef, GAME_QUESTIONS_SUBCOLLECTION);
    
    const q = query(questionsRef, orderBy('roundNumber', 'asc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    } as GameQuestion));
  } catch (error) {
    console.error('Error fetching game questions:', error);
    throw new Error('Failed to fetch game questions');
  }
};

/**
 * Get all incorrectly answered questions from games for repair center
 */
export const getIncorrectGameQuestions = async (
  userId: string,
  certificateId: string
): Promise<Array<{
  questionId: string;
  isCorrect: boolean;
  timeSpent?: number;
  source: 'game';
  gameType: string;
  attemptId: string;
}>> => {
  try {
    const gameAttempts = await getUserGameAttempts(userId, certificateId);
    // Include both completed and in-progress attempts to show incorrect answers immediately
    const relevantAttempts = gameAttempts.filter(a => a.status === 'completed' || a.status === 'in_progress');

    const incorrectQuestions = [];

    for (const attempt of relevantAttempts) {
      if (attempt.id) {
        const gameQuestions = await getGameQuestions(attempt.id);
        
        for (const gameQ of gameQuestions) {
          // Check high bidder's answer
          if (gameQ.highBidderAnswer && !gameQ.highBidderCorrect) {
            incorrectQuestions.push({
              questionId: gameQ.questionId,
              isCorrect: false,
              timeSpent: gameQ.timeSpentAnswer,
              source: 'game' as const,
              gameType: attempt.gameType,
              attemptId: attempt.id
            });
          }
          
          // Check stealer's answer if they attempted
          if (gameQ.stealerAnswer && !gameQ.stealerCorrect) {
            incorrectQuestions.push({
              questionId: gameQ.questionId,
              isCorrect: false,
              timeSpent: gameQ.timeSpentSteal,
              source: 'game' as const,
              gameType: attempt.gameType,
              attemptId: attempt.id
            });
          }
        }
      }
    }
    
    return incorrectQuestions;
  } catch (error) {
    console.error('Error fetching incorrect game questions:', error);
    throw new Error('Failed to fetch incorrect game questions');
  }
};

/**
 * Get incorrectly answered questions from current in-progress games
 * This allows the repair center to show incorrect answers immediately
 */
export const getIncorrectQuestionsFromActiveGames = async (
  userId: string,
  certificateId: string
): Promise<Array<{
  questionId: string;
  isCorrect: boolean;
  timeSpent?: number;
  source: 'game';
  gameType: string;
  attemptId: string;
}>> => {
  try {
    const gameAttempts = await getUserGameAttempts(userId, certificateId);
    const inProgressAttempts = gameAttempts.filter(a => a.status === 'in_progress');

    const incorrectQuestions = [];

    for (const attempt of inProgressAttempts) {
      if (attempt.id) {
        const gameQuestions = await getGameQuestions(attempt.id);

        for (const gameQ of gameQuestions) {
          // Check high bidder's answer
          if (gameQ.highBidderAnswer && !gameQ.highBidderCorrect) {
            incorrectQuestions.push({
              questionId: gameQ.questionId,
              isCorrect: false,
              timeSpent: gameQ.timeSpentAnswer,
              source: 'game' as const,
              gameType: attempt.gameType,
              attemptId: attempt.id
            });
          }

          // Check stealer's answer if they attempted
          if (gameQ.stealerAnswer && !gameQ.stealerCorrect) {
            incorrectQuestions.push({
              questionId: gameQ.questionId,
              isCorrect: false,
              timeSpent: gameQ.timeSpentSteal,
              source: 'game' as const,
              gameType: attempt.gameType,
              attemptId: attempt.id
            });
          }
        }
      }
    }

    return incorrectQuestions;
  } catch (error) {
    console.error('Error fetching incorrect questions from active games:', error);
    throw new Error('Failed to fetch incorrect questions from active games');
  }
};
